{"version": 3, "sources": ["../../../../src/server/web/spec-extension/request.ts"], "sourcesContent": ["import type { I18NConfig } from '../../config-shared'\nimport { NextURL } from '../next-url'\nimport { toNodeOutgoingHttpHeaders, validateURL } from '../utils'\nimport { RemovedUAError, RemovedPageError } from '../error'\nimport { RequestCookies } from './cookies'\n\nexport const INTERNALS = Symbol('internal request')\n\n/**\n * This class extends the [Web `Request` API](https://developer.mozilla.org/docs/Web/API/Request) with additional convenience methods.\n *\n * Read more: [Next.js Docs: `NextRequest`](https://nextjs.org/docs/app/api-reference/functions/next-request)\n */\nexport class NextRequest extends Request {\n  [INTERNALS]: {\n    cookies: RequestCookies\n    url: string\n    nextUrl: NextURL\n  }\n\n  constructor(input: URL | RequestInfo, init: RequestInit = {}) {\n    const url =\n      typeof input !== 'string' && 'url' in input ? input.url : String(input)\n\n    validateURL(url)\n\n    // node Request instance requires duplex option when a body\n    // is present or it errors, we don't handle this for\n    // Request being passed in since it would have already\n    // errored if this wasn't configured\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      if (init.body && init.duplex !== 'half') {\n        init.duplex = 'half'\n      }\n    }\n\n    if (input instanceof Request) super(input, init)\n    else super(url, init)\n\n    const nextUrl = new NextURL(url, {\n      headers: toNodeOutgoingHttpHeaders(this.headers),\n      nextConfig: init.nextConfig,\n    })\n    this[INTERNALS] = {\n      cookies: new RequestCookies(this.headers),\n      nextUrl,\n      url: process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n        ? url\n        : nextUrl.toString(),\n    }\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      cookies: this.cookies,\n      nextUrl: this.nextUrl,\n      url: this.url,\n      // rest of props come from Request\n      bodyUsed: this.bodyUsed,\n      cache: this.cache,\n      credentials: this.credentials,\n      destination: this.destination,\n      headers: Object.fromEntries(this.headers),\n      integrity: this.integrity,\n      keepalive: this.keepalive,\n      method: this.method,\n      mode: this.mode,\n      redirect: this.redirect,\n      referrer: this.referrer,\n      referrerPolicy: this.referrerPolicy,\n      signal: this.signal,\n    }\n  }\n\n  public get cookies() {\n    return this[INTERNALS].cookies\n  }\n\n  public get nextUrl() {\n    return this[INTERNALS].nextUrl\n  }\n\n  /**\n   * @deprecated\n   * `page` has been deprecated in favour of `URLPattern`.\n   * Read more: https://nextjs.org/docs/messages/middleware-request-page\n   */\n  public get page() {\n    throw new RemovedPageError()\n  }\n\n  /**\n   * @deprecated\n   * `ua` has been removed in favour of \\`userAgent\\` function.\n   * Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n   */\n  public get ua() {\n    throw new RemovedUAError()\n  }\n\n  public get url() {\n    return this[INTERNALS].url\n  }\n}\n\nexport interface RequestInit extends globalThis.RequestInit {\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  signal?: AbortSignal\n  // see https://github.com/whatwg/fetch/pull/1457\n  duplex?: 'half'\n}\n"], "names": ["INTERNALS", "NextRequest", "Symbol", "Request", "constructor", "input", "init", "url", "String", "validateURL", "process", "env", "NEXT_RUNTIME", "body", "duplex", "nextUrl", "NextURL", "headers", "toNodeOutgoingHttpHeaders", "nextConfig", "cookies", "RequestCookies", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "toString", "for", "bodyUsed", "cache", "credentials", "destination", "Object", "fromEntries", "integrity", "keepalive", "method", "mode", "redirect", "referrer", "referrerPolicy", "signal", "page", "RemovedPageError", "ua", "RemovedUAError"], "mappings": ";;;;;;;;;;;;;;;IAMaA,SAAS;eAATA;;IAOAC,WAAW;eAAXA;;;yBAZW;uBAC+B;uBACN;yBAClB;AAExB,MAAMD,YAAYE,OAAO;AAOzB,MAAMD,oBAAoBE;IAO/BC,YAAYC,KAAwB,EAAEC,OAAoB,CAAC,CAAC,CAAE;QAC5D,MAAMC,MACJ,OAAOF,UAAU,YAAY,SAASA,QAAQA,MAAME,GAAG,GAAGC,OAAOH;QAEnEI,IAAAA,kBAAW,EAACF;QAEZ,2DAA2D;QAC3D,oDAAoD;QACpD,sDAAsD;QACtD,oCAAoC;QACpC,IAAIG,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YACvC,IAAIN,KAAKO,IAAI,IAAIP,KAAKQ,MAAM,KAAK,QAAQ;gBACvCR,KAAKQ,MAAM,GAAG;YAChB;QACF;QAEA,IAAIT,iBAAiBF,SAAS,KAAK,CAACE,OAAOC;aACtC,KAAK,CAACC,KAAKD;QAEhB,MAAMS,UAAU,IAAIC,gBAAO,CAACT,KAAK;YAC/BU,SAASC,IAAAA,gCAAyB,EAAC,IAAI,CAACD,OAAO;YAC/CE,YAAYb,KAAKa,UAAU;QAC7B;QACA,IAAI,CAACnB,UAAU,GAAG;YAChBoB,SAAS,IAAIC,uBAAc,CAAC,IAAI,CAACJ,OAAO;YACxCF;YACAR,KAAKG,QAAQC,GAAG,CAACW,kCAAkC,GAC/Cf,MACAQ,QAAQQ,QAAQ;QACtB;IACF;IAEA,CAACrB,OAAOsB,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLJ,SAAS,IAAI,CAACA,OAAO;YACrBL,SAAS,IAAI,CAACA,OAAO;YACrBR,KAAK,IAAI,CAACA,GAAG;YACb,kCAAkC;YAClCkB,UAAU,IAAI,CAACA,QAAQ;YACvBC,OAAO,IAAI,CAACA,KAAK;YACjBC,aAAa,IAAI,CAACA,WAAW;YAC7BC,aAAa,IAAI,CAACA,WAAW;YAC7BX,SAASY,OAAOC,WAAW,CAAC,IAAI,CAACb,OAAO;YACxCc,WAAW,IAAI,CAACA,SAAS;YACzBC,WAAW,IAAI,CAACA,SAAS;YACzBC,QAAQ,IAAI,CAACA,MAAM;YACnBC,MAAM,IAAI,CAACA,IAAI;YACfC,UAAU,IAAI,CAACA,QAAQ;YACvBC,UAAU,IAAI,CAACA,QAAQ;YACvBC,gBAAgB,IAAI,CAACA,cAAc;YACnCC,QAAQ,IAAI,CAACA,MAAM;QACrB;IACF;IAEA,IAAWlB,UAAU;QACnB,OAAO,IAAI,CAACpB,UAAU,CAACoB,OAAO;IAChC;IAEA,IAAWL,UAAU;QACnB,OAAO,IAAI,CAACf,UAAU,CAACe,OAAO;IAChC;IAEA;;;;GAIC,GACD,IAAWwB,OAAO;QAChB,MAAM,IAAIC,uBAAgB;IAC5B;IAEA;;;;GAIC,GACD,IAAWC,KAAK;QACd,MAAM,IAAIC,qBAAc;IAC1B;IAEA,IAAWnC,MAAM;QACf,OAAO,IAAI,CAACP,UAAU,CAACO,GAAG;IAC5B;AACF"}