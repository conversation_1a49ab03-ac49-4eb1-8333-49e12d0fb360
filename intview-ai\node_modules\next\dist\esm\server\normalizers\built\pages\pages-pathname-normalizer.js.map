{"version": 3, "sources": ["../../../../../src/server/normalizers/built/pages/pages-pathname-normalizer.ts"], "sourcesContent": ["import { PAGE_TYPES } from '../../../../lib/page-types'\nimport { AbsoluteFilenameNormalizer } from '../../absolute-filename-normalizer'\n\nexport class DevPagesPathnameNormalizer extends AbsoluteFilenameNormalizer {\n  constructor(pagesDir: string, extensions: ReadonlyArray<string>) {\n    super(pagesDir, extensions, PAGE_TYPES.PAGES)\n  }\n}\n"], "names": ["PAGE_TYPES", "AbsoluteFilenameNormalizer", "DevPagesPathnameNormalizer", "constructor", "pagesDir", "extensions", "PAGES"], "mappings": "AAAA,SAASA,UAAU,QAAQ,6BAA4B;AACvD,SAASC,0BAA0B,QAAQ,qCAAoC;AAE/E,OAAO,MAAMC,mCAAmCD;IAC9CE,YAAYC,QAAgB,EAAEC,UAAiC,CAAE;QAC/D,KAAK,CAACD,UAAUC,YAAYL,WAAWM,KAAK;IAC9C;AACF"}