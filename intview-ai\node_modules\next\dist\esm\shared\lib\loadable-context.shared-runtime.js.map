{"version": 3, "sources": ["../../../src/shared/lib/loadable-context.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport React from 'react'\n\ntype CaptureFn = (moduleName: string) => void\n\nexport const LoadableContext = React.createContext<CaptureFn | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  LoadableContext.displayName = 'LoadableContext'\n}\n"], "names": ["React", "LoadableContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAAA;AAEA,OAAOA,WAAW,QAAO;AAIzB,OAAO,MAAMC,kBAAkBD,MAAME,aAAa,CAAmB,MAAK;AAE1E,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCJ,gBAAgBK,WAAW,GAAG;AAChC"}