{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\r\nimport GitHub from \"next-auth/providers/github\";\r\nimport Google from \"next-auth/providers/google\";\r\n\r\nexport const { handlers, signIn, signOut, auth } = NextAuth({\r\n  providers: [GitHub, Google],\r\n  secret: process.env.AUTH_SECRET,\r\n});\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,UAAQ,AAAD,EAAE;IAC1D,WAAW;QAAC,uJAAA,CAAA,UAAM;QAAE,uJAAA,CAAA,UAAM;KAAC;IAC3B,QAAQ,QAAQ,GAAG,CAAC,WAAW;AACjC", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import { handlers } from \"@/auth\"; // Referring to the auth.ts we just created\r\nexport const { GET, POST } = handlers;\r\n"], "names": [], "mappings": ";;;;AAAA,oLAAmC,2CAA2C;;AACvE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,sGAAA,CAAA,WAAQ", "debugId": null}}]}