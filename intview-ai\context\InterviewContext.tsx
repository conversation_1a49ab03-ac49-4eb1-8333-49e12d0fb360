"use client";
import React, { createContext, useContext, useState, useCallback, ReactNode } from "react";
import { interviewApi, ConversationMessage } from "@/lib/interview-api";

interface InterviewContextType {
  // Interview state
  currentQuestion: string;
  setCurrentQuestion: (question: string) => void;
  isInterviewStarted: boolean;
  setIsInterviewStarted: (started: boolean) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;

  // Interview data
  candidateName: string;
  setCandidateName: (name: string) => void;
  jobTitle: string;
  setJobTitle: (title: string) => void;
  experience: number;
  setExperience: (exp: number) => void;

  // Conversation history
  conversationHistory: ConversationMessage[];
  addToHistory: (message: ConversationMessage) => void;

  // API methods
  startInterview: () => Promise<void>;
  submitAnswer: (answer: string) => Promise<void>;

  // Error handling
  error: string | null;
  setError: (error: string | null) => void;
}

const InterviewContext = createContext<InterviewContextType | undefined>(undefined);

interface InterviewProviderProps {
  children: ReactNode;
}

export const InterviewProvider: React.FC<InterviewProviderProps> = ({ children }) => {
  // Interview state
  const [currentQuestion, setCurrentQuestion] = useState<string>("");
  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Interview data
  const [candidateName, setCandidateName] = useState<string>("Jonathan");
  const [jobTitle, setJobTitle] = useState<string>("Insurance Agent");
  const [experience, setExperience] = useState<number>(3);

  // Conversation history
  const [conversationHistory, setConversationHistory] = useState<ConversationMessage[]>([]);

  // Error handling
  const [error, setError] = useState<string | null>(null);

  // Helper function to add messages to conversation history
  const addToHistory = useCallback((message: ConversationMessage) => {
    setConversationHistory(prev => [...prev, message]);
  }, []);

  // Start the interview by getting the first question
  const startInterview = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const firstQuestion = await interviewApi.startInterview(jobTitle, candidateName, experience);
      setCurrentQuestion(firstQuestion);

      // Add interviewer's first question to history
      addToHistory({
        role: 'interviewer',
        content: firstQuestion
      });

      setIsInterviewStarted(true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to start interview";
      setError(errorMessage);
      console.error("Failed to start interview:", err);
    } finally {
      setIsLoading(false);
    }
  }, [jobTitle, candidateName, experience, addToHistory]);

  // Submit candidate's answer and get next question
  const submitAnswer = useCallback(async (answer: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Add candidate's answer to history
      const candidateMessage: ConversationMessage = {
        role: 'candidate',
        content: answer
      };

      const updatedHistory = [...conversationHistory, candidateMessage];
      addToHistory(candidateMessage);

      // Get next question from API
      const nextQuestion = await interviewApi.continueInterview(
        jobTitle,
        candidateName,
        experience,
        updatedHistory
      );

      setCurrentQuestion(nextQuestion);

      // Add interviewer's next question to history
      addToHistory({
        role: 'interviewer',
        content: nextQuestion
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to submit answer";
      setError(errorMessage);
      console.error("Failed to submit answer:", err);
    } finally {
      setIsLoading(false);
    }
  }, [jobTitle, candidateName, experience, conversationHistory, addToHistory]);

  const value: InterviewContextType = {
    // Interview state
    currentQuestion,
    setCurrentQuestion,
    isInterviewStarted,
    setIsInterviewStarted,
    isLoading,
    setIsLoading,

    // Interview data
    candidateName,
    setCandidateName,
    jobTitle,
    setJobTitle,
    experience,
    setExperience,

    // Conversation history
    conversationHistory,
    addToHistory,

    // API methods
    startInterview,
    submitAnswer,

    // Error handling
    error,
    setError,
  };

  return (
    <InterviewContext.Provider value={value}>
      {children}
    </InterviewContext.Provider>
  );
};

export const useInterview = (): InterviewContextType => {
  const context = useContext(InterviewContext);
  if (context === undefined) {
    throw new Error('useInterview must be used within an InterviewProvider');
  }
  return context;
};
