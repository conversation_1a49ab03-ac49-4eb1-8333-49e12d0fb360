"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InterviewService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const generative_ai_1 = require("@google/generative-ai");
let InterviewService = class InterviewService {
    constructor(configService) {
        this.configService = configService;
        const apiKey = this.configService.get('GEMINI_API_KEY');
        if (!apiKey) {
            throw new Error('GEMINI_API_KEY environment variable is not set');
        }
        console.log('Initializing Gemini with API key:', apiKey.substring(0, 10) + '...');
        this.gemini = new generative_ai_1.GoogleGenerativeAI(apiKey);
    }
    async runInterview(interviewData) {
        const { position, name, experience, history } = interviewData;
        const formattedHistory = history.length > 0
            ? history.map(msg => `${msg.role === 'candidate' ? 'Candidate' : 'Interviewer'}: ${msg.content}`).join('\n')
            : 'No previous conversation.';
        const prompt = `
You are a professional technical interviewer.

Details:
- Position: ${position}
- Candidate Name: ${name}
- Experience: ${experience} years
- Interview Style: Conversational, professional, encouraging
- Keep it role-specific and relevant to their experience level.

Interview Rules:
1. Ask one question at a time.
2. Adapt difficulty based on candidate's answers.
3. Start with a simple warm-up question.
4. Avoid yes/no questions — ask open-ended ones.
5. After each answer, evaluate briefly (score 1–5 and note reasoning).
6. Continue until you've asked about:
   - Technical skills (React, JavaScript, HTML/CSS)
   - Problem-solving
   - Past project experience
   - Soft skills/teamwork
7. At the end, provide a summary:
   - Strengths
   - Weaknesses
   - Suggested hiring decision

Conversation so far:
${formattedHistory}

Now, ask the next best question or, if done, provide the final evaluation.
`;
        try {
            const model = this.gemini.getGenerativeModel({ model: 'gemini-2.0-flash' });
            const result = await model.generateContent(prompt);
            const responseText = result.response.text();
            return { response: responseText };
        }
        catch (error) {
            console.error('Gemini API Error:', error);
            if (error.message?.includes('API key not valid')) {
                throw new Error('Invalid Gemini API key. Please check your GEMINI_API_KEY environment variable.');
            }
            else if (error.message?.includes('quota')) {
                throw new Error('Gemini API quota exceeded. Please check your usage limits.');
            }
            else {
                throw new Error(`Failed to generate interview response: ${error.message || 'Unknown error'}`);
            }
        }
    }
};
exports.InterviewService = InterviewService;
exports.InterviewService = InterviewService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], InterviewService);
//# sourceMappingURL=interview.service.js.map