{"version": 3, "sources": ["../../../../../src/server/normalizers/built/pages/index.ts"], "sourcesContent": ["import {\n  DevPagesBundlePathNormalizer,\n  PagesBundlePathNormalizer,\n} from './pages-bundle-path-normalizer'\nimport { PagesFilenameNormalizer } from './pages-filename-normalizer'\nimport { DevPagesPageNormalizer } from './pages-page-normalizer'\nimport { DevPagesPathnameNormalizer } from './pages-pathname-normalizer'\n\nexport class PagesNormalizers {\n  public readonly filename: PagesFilenameNormalizer\n  public readonly bundlePath: PagesBundlePathNormalizer\n\n  constructor(distDir: string) {\n    this.filename = new PagesFilenameNormalizer(distDir)\n    this.bundlePath = new PagesBundlePathNormalizer()\n\n    // You'd think that we'd require a `pathname` normalizer here, but for\n    // `/pages` we have to handle i18n routes, which means that we need to\n    // analyze the page path to determine the locale prefix and it's locale.\n  }\n}\n\nexport class DevPagesNormalizers {\n  public readonly page: DevPagesPageNormalizer\n  public readonly pathname: DevPagesPathnameNormalizer\n  public readonly bundlePath: Dev<PERSON>agesBundlePathNormalizer\n\n  constructor(pagesDir: string, extensions: ReadonlyArray<string>) {\n    this.page = new DevPagesPageNormalizer(pagesDir, extensions)\n    this.pathname = new DevPagesPathnameNormalizer(pagesDir, extensions)\n    this.bundlePath = new DevPagesBundlePathNormalizer(this.page)\n  }\n}\n"], "names": ["DevPagesBundlePathNormalizer", "PagesBundlePathNormalizer", "PagesFilenameNormalizer", "DevPagesPageNormalizer", "DevPagesPathnameNormalizer", "PagesNormalizers", "constructor", "distDir", "filename", "bundlePath", "DevPagesNormalizers", "pagesDir", "extensions", "page", "pathname"], "mappings": "AAAA,SACEA,4BAA4B,EAC5BC,yBAAyB,QACpB,iCAAgC;AACvC,SAASC,uBAAuB,QAAQ,8BAA6B;AACrE,SAASC,sBAAsB,QAAQ,0BAAyB;AAChE,SAASC,0BAA0B,QAAQ,8BAA6B;AAExE,OAAO,MAAMC;IAIXC,YAAYC,OAAe,CAAE;QAC3B,IAAI,CAACC,QAAQ,GAAG,IAAIN,wBAAwBK;QAC5C,IAAI,CAACE,UAAU,GAAG,IAAIR;IAEtB,sEAAsE;IACtE,sEAAsE;IACtE,wEAAwE;IAC1E;AACF;AAEA,OAAO,MAAMS;IAKXJ,YAAYK,QAAgB,EAAEC,UAAiC,CAAE;QAC/D,IAAI,CAACC,IAAI,GAAG,IAAIV,uBAAuBQ,UAAUC;QACjD,IAAI,CAACE,QAAQ,GAAG,IAAIV,2BAA2BO,UAAUC;QACzD,IAAI,CAACH,UAAU,GAAG,IAAIT,6BAA6B,IAAI,CAACa,IAAI;IAC9D;AACF"}