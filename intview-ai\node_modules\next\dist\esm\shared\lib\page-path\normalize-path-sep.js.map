{"version": 3, "sources": ["../../../../src/shared/lib/page-path/normalize-path-sep.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is no backslash\n * escaping slashes in the path. Example:\n *  - `foo\\/bar\\/baz` -> `foo/bar/baz`\n */\nexport function normalizePathSep(path: string): string {\n  return path.replace(/\\\\/g, '/')\n}\n"], "names": ["normalizePathSep", "path", "replace"], "mappings": "AAAA;;;;CAIC,GACD,OAAO,SAASA,iBAAiBC,IAAY;IAC3C,OAAOA,KAAKC,OAAO,CAAC,OAAO;AAC7B"}