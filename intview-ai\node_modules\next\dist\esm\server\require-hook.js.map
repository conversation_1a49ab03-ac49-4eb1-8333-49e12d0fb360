{"version": 3, "sources": ["../../src/server/require-hook.ts"], "sourcesContent": ["// Synchronously inject a require hook for webpack and webpack/. It's required to use the internal ncc webpack version.\n// This is needed for userland plugins to attach to the same webpack instance as Next.js'.\n// Individually compiled modules are as defined for the compilation in bundles/webpack/packages/*.\n\n// This module will only be loaded once per process.\nconst path = require('path')\nconst mod = require('module')\nconst originalRequire = mod.prototype.require\nconst resolveFilename = mod._resolveFilename\n\nlet resolve: typeof require.resolve = process.env.NEXT_MINIMAL\n  ? // @ts-ignore\n    __non_webpack_require__.resolve\n  : require.resolve\n\nexport const hookPropertyMap = new Map()\n\nexport const defaultOverrides = {\n  'styled-jsx': path.dirname(resolve('styled-jsx/package.json')),\n  'styled-jsx/style': resolve('styled-jsx/style'),\n  'styled-jsx/style.js': resolve('styled-jsx/style'),\n}\n\nconst toResolveMap = (map: Record<string, string>): [string, string][] =>\n  Object.entries(map).map(([key, value]) => [key, resolve(value)])\n\nexport function addHookAliases(aliases: [string, string][] = []) {\n  for (const [key, value] of aliases) {\n    hookPropertyMap.set(key, value)\n  }\n}\n\naddHookAliases(toResolveMap(defaultOverrides))\n\nmod._resolveFilename = function (\n  originalResolveFilename: (\n    request: string,\n    parent: string,\n    isMain: boolean,\n    opts: any\n  ) => string,\n  requestMap: Map<string, string>,\n  request: string,\n  parent: string,\n  isMain: boolean,\n  options: any\n) {\n  const hookResolved = requestMap.get(request)\n  if (hookResolved) request = hookResolved\n\n  return originalResolveFilename.call(mod, request, parent, isMain, options)\n\n  // We use `bind` here to avoid referencing outside variables to create potential memory leaks.\n}.bind(null, resolveFilename, hookPropertyMap)\n\n// This is a hack to make sure that if a user requires a Next.js module that wasn't bundled\n// that needs to point to the rendering runtime version, it will point to the correct one.\n// This can happen on `pages` when a user requires a dependency that uses next/image for example.\nmod.prototype.require = function (request: string) {\n  if (request.endsWith('.shared-runtime')) {\n    return originalRequire.call(\n      this,\n      `next/dist/server/route-modules/pages/vendored/contexts/${path.basename(\n        request,\n        '.shared-runtime'\n      )}`\n    )\n  }\n\n  return originalRequire.call(this, request)\n}\n"], "names": ["path", "require", "mod", "originalRequire", "prototype", "resolveFilename", "_resolveFilename", "resolve", "process", "env", "NEXT_MINIMAL", "__non_webpack_require__", "hookPropertyMap", "Map", "defaultOverrides", "dirname", "toResolveMap", "map", "Object", "entries", "key", "value", "addHookAliases", "aliases", "set", "originalResolveFilename", "requestMap", "request", "parent", "is<PERSON><PERSON>", "options", "hookResolved", "get", "call", "bind", "endsWith", "basename"], "mappings": "AAAA,uHAAuH;AACvH,0FAA0F;AAC1F,kGAAkG;AAElG,oDAAoD;AACpD,MAAMA,OAAOC,QAAQ;AACrB,MAAMC,MAAMD,QAAQ;AACpB,MAAME,kBAAkBD,IAAIE,SAAS,CAACH,OAAO;AAC7C,MAAMI,kBAAkBH,IAAII,gBAAgB;AAE5C,IAAIC,UAAkCC,QAAQC,GAAG,CAACC,YAAY,GAE1DC,wBAAwBJ,OAAO,GAC/BN,QAAQM,OAAO;AAEnB,OAAO,MAAMK,kBAAkB,IAAIC,MAAK;AAExC,OAAO,MAAMC,mBAAmB;IAC9B,cAAcd,KAAKe,OAAO,CAACR,QAAQ;IACnC,oBAAoBA,QAAQ;IAC5B,uBAAuBA,QAAQ;AACjC,EAAC;AAED,MAAMS,eAAe,CAACC,MACpBC,OAAOC,OAAO,CAACF,KAAKA,GAAG,CAAC,CAAC,CAACG,KAAKC,MAAM,GAAK;YAACD;YAAKb,QAAQc;SAAO;AAEjE,OAAO,SAASC,eAAeC,UAA8B,EAAE;IAC7D,KAAK,MAAM,CAACH,KAAKC,MAAM,IAAIE,QAAS;QAClCX,gBAAgBY,GAAG,CAACJ,KAAKC;IAC3B;AACF;AAEAC,eAAeN,aAAaF;AAE5BZ,IAAII,gBAAgB,GAAG,CAAA,SACrBmB,uBAKW,EACXC,UAA+B,EAC/BC,OAAe,EACfC,MAAc,EACdC,MAAe,EACfC,OAAY;IAEZ,MAAMC,eAAeL,WAAWM,GAAG,CAACL;IACpC,IAAII,cAAcJ,UAAUI;IAE5B,OAAON,wBAAwBQ,IAAI,CAAC/B,KAAKyB,SAASC,QAAQC,QAAQC;AAElE,8FAA8F;AAChG,CAAA,EAAEI,IAAI,CAAC,MAAM7B,iBAAiBO;AAE9B,2FAA2F;AAC3F,0FAA0F;AAC1F,iGAAiG;AACjGV,IAAIE,SAAS,CAACH,OAAO,GAAG,SAAU0B,OAAe;IAC/C,IAAIA,QAAQQ,QAAQ,CAAC,oBAAoB;QACvC,OAAOhC,gBAAgB8B,IAAI,CACzB,IAAI,EACJ,CAAC,uDAAuD,EAAEjC,KAAKoC,QAAQ,CACrET,SACA,oBACC;IAEP;IAEA,OAAOxB,gBAAgB8B,IAAI,CAAC,IAAI,EAAEN;AACpC"}