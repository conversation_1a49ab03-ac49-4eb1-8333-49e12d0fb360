{"version": 3, "sources": ["../../src/telemetry/project-id.ts"], "sourcesContent": ["import { exec } from 'child_process'\n\n// Q: Why does Next.js need a project ID? Why is it looking at my git remote?\n// A:\n// Next.js' telemetry is and always will be completely anonymous. Because of\n// this, we need a way to differentiate different projects to track feature\n// usage accurately. For example, to prevent a feature from appearing to be\n// constantly `used` and then `unused` when switching between local projects.\n// To reiterate,\n// we **never** can read your actual git remote. The value is hashed one-way\n// with random salt data, making it impossible for us to reverse or try to\n// guess the remote by re-computing hashes.\n\nasync function _getProjectIdByGit() {\n  try {\n    let resolve: (value: Buffer | string) => void, reject: (err: Error) => void\n    const promise = new Promise<Buffer | string>((res, rej) => {\n      resolve = res\n      reject = rej\n    })\n\n    exec(\n      `git config --local --get remote.origin.url`,\n      {\n        timeout: 1000,\n        windowsHide: true,\n      },\n      (error: null | Error, stdout: Buffer | string) => {\n        if (error) {\n          reject(error)\n          return\n        }\n        resolve(stdout)\n      }\n    )\n\n    return String(await promise).trim()\n  } catch (_) {\n    return null\n  }\n}\n\nexport async function getRawProjectId(): Promise<string> {\n  return (\n    (await _getProjectIdByGit()) || process.env.REPOSITORY_URL || process.cwd()\n  )\n}\n"], "names": ["getRawProjectId", "_getProjectIdByGit", "resolve", "reject", "promise", "Promise", "res", "rej", "exec", "timeout", "windowsHide", "error", "stdout", "String", "trim", "_", "process", "env", "REPOSITORY_URL", "cwd"], "mappings": ";;;;+BA0CsBA;;;eAAAA;;;+BA1CD;AAErB,6EAA6E;AAC7E,KAAK;AACL,4EAA4E;AAC5E,2EAA2E;AAC3E,2EAA2E;AAC3E,6EAA6E;AAC7E,gBAAgB;AAChB,4EAA4E;AAC5E,0EAA0E;AAC1E,2CAA2C;AAE3C,eAAeC;IACb,IAAI;QACF,IAAIC,SAA2CC;QAC/C,MAAMC,UAAU,IAAIC,QAAyB,CAACC,KAAKC;YACjDL,UAAUI;YACVH,SAASI;QACX;QAEAC,IAAAA,mBAAI,EACF,CAAC,0CAA0C,CAAC,EAC5C;YACEC,SAAS;YACTC,aAAa;QACf,GACA,CAACC,OAAqBC;YACpB,IAAID,OAAO;gBACTR,OAAOQ;gBACP;YACF;YACAT,QAAQU;QACV;QAGF,OAAOC,OAAO,MAAMT,SAASU,IAAI;IACnC,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF;AAEO,eAAef;IACpB,OACE,AAAC,MAAMC,wBAAyBe,QAAQC,GAAG,CAACC,cAAc,IAAIF,QAAQG,GAAG;AAE7E"}