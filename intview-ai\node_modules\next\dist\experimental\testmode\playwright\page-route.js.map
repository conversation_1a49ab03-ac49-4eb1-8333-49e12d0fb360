{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/page-route.ts"], "sourcesContent": ["import type {\n  Page,\n  Route,\n  Request as PlaywrightRequest,\n} from '@playwright/test'\nimport type { <PERSON>tch<PERSON><PERSON><PERSON> } from './next-worker-fixture'\n\nfunction continueRoute(\n  route: Route,\n  request: PlaywrightRequest,\n  testHeaders: Record<string, string>\n): Promise<void> {\n  return route.continue({\n    headers: {\n      ...request.headers(),\n      ...testHeaders,\n    },\n  })\n}\n\nexport async function handleRoute(\n  route: Route,\n  page: Page,\n  testHeaders: Record<string, string>,\n  fetchHandler: FetchHandler | null\n) {\n  const request = route.request()\n\n  // Continue the navigation and non-fetch requests.\n  if (request.isNavigationRequest() || request.resourceType() !== 'fetch') {\n    return continueRoute(route, request, testHeaders)\n  }\n\n  // Continue the local requests. The followup requests will be intercepted\n  // on the server.\n  const pageOrigin = new URL(page.url()).origin\n  const requestOrigin = new URL(request.url()).origin\n  if (pageOrigin === requestOrigin) {\n    return continueRoute(route, request, testHeaders)\n  }\n\n  if (!fetchHandler) {\n    return route.abort()\n  }\n\n  const postData = request.postDataBuffer()\n  const fetchRequest = new Request(request.url(), {\n    method: request.method(),\n    headers: Object.fromEntries(\n      Object.entries(request.headers()).filter(\n        ([name]) => !name.toLowerCase().startsWith('next-test-')\n      )\n    ),\n    body: postData ?? null,\n  })\n\n  const proxyResponse = await fetchHandler(fetchRequest)\n  if (!proxyResponse) {\n    return route.abort()\n  }\n  if (proxyResponse === 'abort') {\n    return route.abort()\n  }\n  if (proxyResponse === 'continue') {\n    return continueRoute(route, request, testHeaders)\n  }\n  const { status, headers, body } = proxyResponse\n  return route.fulfill({\n    status,\n    headers: Object.fromEntries(headers),\n    body: body ? Buffer.from(await proxyResponse.arrayBuffer()) : undefined,\n  })\n}\n"], "names": ["handleRoute", "continueRoute", "route", "request", "testHeaders", "continue", "headers", "page", "<PERSON><PERSON><PERSON><PERSON>", "isNavigationRequest", "resourceType", "page<PERSON><PERSON><PERSON>", "URL", "url", "origin", "<PERSON><PERSON><PERSON><PERSON>", "abort", "postData", "postDataBuffer", "fetchRequest", "Request", "method", "Object", "fromEntries", "entries", "filter", "name", "toLowerCase", "startsWith", "body", "proxyResponse", "status", "fulfill", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "undefined"], "mappings": ";;;;+BAoBsBA;;;eAAAA;;;AAbtB,SAASC,cACPC,KAAY,EACZC,OAA0B,EAC1BC,WAAmC;IAEnC,OAAOF,MAAMG,QAAQ,CAAC;QACpBC,SAAS;YACP,GAAGH,QAAQG,OAAO,EAAE;YACpB,GAAGF,WAAW;QAChB;IACF;AACF;AAEO,eAAeJ,YACpBE,KAAY,EACZK,IAAU,EACVH,WAAmC,EACnCI,YAAiC;IAEjC,MAAML,UAAUD,MAAMC,OAAO;IAE7B,kDAAkD;IAClD,IAAIA,QAAQM,mBAAmB,MAAMN,QAAQO,YAAY,OAAO,SAAS;QACvE,OAAOT,cAAcC,OAAOC,SAASC;IACvC;IAEA,yEAAyE;IACzE,iBAAiB;IACjB,MAAMO,aAAa,IAAIC,IAAIL,KAAKM,GAAG,IAAIC,MAAM;IAC7C,MAAMC,gBAAgB,IAAIH,IAAIT,QAAQU,GAAG,IAAIC,MAAM;IACnD,IAAIH,eAAeI,eAAe;QAChC,OAAOd,cAAcC,OAAOC,SAASC;IACvC;IAEA,IAAI,CAACI,cAAc;QACjB,OAAON,MAAMc,KAAK;IACpB;IAEA,MAAMC,WAAWd,QAAQe,cAAc;IACvC,MAAMC,eAAe,IAAIC,QAAQjB,QAAQU,GAAG,IAAI;QAC9CQ,QAAQlB,QAAQkB,MAAM;QACtBf,SAASgB,OAAOC,WAAW,CACzBD,OAAOE,OAAO,CAACrB,QAAQG,OAAO,IAAImB,MAAM,CACtC,CAAC,CAACC,KAAK,GAAK,CAACA,KAAKC,WAAW,GAAGC,UAAU,CAAC;QAG/CC,MAAMZ,YAAY;IACpB;IAEA,MAAMa,gBAAgB,MAAMtB,aAAaW;IACzC,IAAI,CAACW,eAAe;QAClB,OAAO5B,MAAMc,KAAK;IACpB;IACA,IAAIc,kBAAkB,SAAS;QAC7B,OAAO5B,MAAMc,KAAK;IACpB;IACA,IAAIc,kBAAkB,YAAY;QAChC,OAAO7B,cAAcC,OAAOC,SAASC;IACvC;IACA,MAAM,EAAE2B,MAAM,EAAEzB,OAAO,EAAEuB,IAAI,EAAE,GAAGC;IAClC,OAAO5B,MAAM8B,OAAO,CAAC;QACnBD;QACAzB,SAASgB,OAAOC,WAAW,CAACjB;QAC5BuB,MAAMA,OAAOI,OAAOC,IAAI,CAAC,MAAMJ,cAAcK,WAAW,MAAMC;IAChE;AACF"}