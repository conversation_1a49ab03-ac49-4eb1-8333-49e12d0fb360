{"version": 3, "sources": ["../../../../src/shared/lib/turbopack/manifest-loader.ts"], "sourcesContent": ["import type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from '../../../build/webpack/plugins/middleware-plugin'\nimport type {\n  StatsAsset,\n  StatsChunk,\n  StatsChunkGroup,\n  StatsModule,\n  StatsCompilation as WebpackStats,\n} from 'webpack'\nimport type { BuildManifest } from '../../../server/get-page-files'\nimport type { AppBuildManifest } from '../../../build/webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from '../../../build/webpack/plugins/pages-manifest-plugin'\nimport { pathToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport type { ActionManifest } from '../../../build/webpack/plugins/flight-client-entry-plugin'\nimport type { NextFontManifest } from '../../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { REACT_LOADABLE_MANIFEST } from '../constants'\nimport {\n  APP_BUILD_MANIFEST,\n  APP_PATHS_MANIFEST,\n  BUILD_MANIFEST,\n  INTERCEPTION_ROUTE_REWRITE_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  NEXT_FONT_MANIFEST,\n  PAGES_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n  WEBPACK_STATS,\n} from '../constants'\nimport { join, posix } from 'path'\nimport { readFile } from 'fs/promises'\nimport type { SetupOpts } from '../../../server/lib/router-utils/setup-dev-bundler'\nimport { deleteCache } from '../../../server/dev/require-cache'\nimport { writeFileAtomic } from '../../../lib/fs/write-atomic'\nimport { isInterceptionRouteRewrite } from '../../../lib/generate-interception-routes-rewrites'\nimport {\n  type ClientBuildManifest,\n  normalizeRewritesForBuildManifest,\n  srcEmptySsgManifest,\n  processRoute,\n  createEdgeRuntimeManifest,\n} from '../../../build/webpack/plugins/build-manifest-plugin'\nimport getAssetPathFromRoute from '../router/utils/get-asset-path-from-route'\nimport { getEntryKey, type EntryKey } from './entry-key'\nimport type { CustomRoutes } from '../../../lib/load-custom-routes'\nimport { getSortedRoutes } from '../router/utils'\nimport { existsSync } from 'fs'\nimport {\n  addMetadataIdToRoute,\n  addRouteSuffix,\n  removeRouteSuffix,\n} from '../../../server/dev/turbopack-utils'\nimport { tryToParsePath } from '../../../lib/try-to-parse-path'\nimport type { Entrypoints } from '../../../build/swc/types'\n\ninterface InstrumentationDefinition {\n  files: string[]\n  name: 'instrumentation'\n}\n\ntype TurbopackMiddlewareManifest = MiddlewareManifest & {\n  instrumentation?: InstrumentationDefinition\n}\n\ntype ManifestName =\n  | typeof MIDDLEWARE_MANIFEST\n  | typeof BUILD_MANIFEST\n  | typeof APP_BUILD_MANIFEST\n  | typeof PAGES_MANIFEST\n  | typeof WEBPACK_STATS\n  | typeof APP_PATHS_MANIFEST\n  | `${typeof SERVER_REFERENCE_MANIFEST}.json`\n  | `${typeof NEXT_FONT_MANIFEST}.json`\n  | typeof REACT_LOADABLE_MANIFEST\n\nconst getManifestPath = (\n  page: string,\n  distDir: string,\n  name: ManifestName,\n  type: string,\n  firstCall: boolean\n) => {\n  let manifestPath = posix.join(\n    distDir,\n    `server`,\n    type,\n    type === 'middleware' || type === 'instrumentation'\n      ? ''\n      : type === 'app'\n        ? page\n        : getAssetPathFromRoute(page),\n    name\n  )\n\n  if (firstCall) {\n    const isSitemapRoute = /[\\\\/]sitemap(.xml)?\\/route$/.test(page)\n    // Check the ambiguity of /sitemap and /sitemap.xml\n    if (isSitemapRoute && !existsSync(manifestPath)) {\n      manifestPath = getManifestPath(\n        page.replace(/\\/sitemap\\/route$/, '/sitemap.xml/route'),\n        distDir,\n        name,\n        type,\n        false\n      )\n    }\n    // existsSync is faster than using the async version\n    if (!existsSync(manifestPath) && page.endsWith('/route')) {\n      // TODO: Improve implementation of metadata routes, currently it requires this extra check for the variants of the files that can be written.\n      let metadataPage = addRouteSuffix(\n        addMetadataIdToRoute(removeRouteSuffix(page))\n      )\n      manifestPath = getManifestPath(metadataPage, distDir, name, type, false)\n    }\n  }\n\n  return manifestPath\n}\n\nasync function readPartialManifest<T>(\n  distDir: string,\n  name: ManifestName,\n  pageName: string,\n  type: 'pages' | 'app' | 'middleware' | 'instrumentation' = 'pages'\n): Promise<T> {\n  const page = pageName\n  const manifestPath = getManifestPath(page, distDir, name, type, true)\n  return JSON.parse(await readFile(posix.join(manifestPath), 'utf-8')) as T\n}\n\nexport class TurbopackManifestLoader {\n  private actionManifests: Map<EntryKey, ActionManifest> = new Map()\n  private appBuildManifests: Map<EntryKey, AppBuildManifest> = new Map()\n  private appPathsManifests: Map<EntryKey, PagesManifest> = new Map()\n  private buildManifests: Map<EntryKey, BuildManifest> = new Map()\n  private fontManifests: Map<EntryKey, NextFontManifest> = new Map()\n  private middlewareManifests: Map<EntryKey, TurbopackMiddlewareManifest> =\n    new Map()\n  private pagesManifests: Map<string, PagesManifest> = new Map()\n  private webpackStats: Map<EntryKey, WebpackStats> = new Map()\n  private encryptionKey: string\n\n  private readonly distDir: string\n  private readonly buildId: string\n\n  constructor({\n    distDir,\n    buildId,\n    encryptionKey,\n  }: {\n    buildId: string\n    distDir: string\n    encryptionKey: string\n  }) {\n    this.distDir = distDir\n    this.buildId = buildId\n    this.encryptionKey = encryptionKey\n  }\n\n  delete(key: EntryKey) {\n    this.actionManifests.delete(key)\n    this.appBuildManifests.delete(key)\n    this.appPathsManifests.delete(key)\n    this.buildManifests.delete(key)\n    this.fontManifests.delete(key)\n    this.middlewareManifests.delete(key)\n    this.pagesManifests.delete(key)\n    this.webpackStats.delete(key)\n  }\n\n  async loadActionManifest(pageName: string): Promise<void> {\n    this.actionManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${SERVER_REFERENCE_MANIFEST}.json`,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async mergeActionManifests(manifests: Iterable<ActionManifest>) {\n    type ActionEntries = ActionManifest['edge' | 'node']\n    const manifest: ActionManifest = {\n      node: {},\n      edge: {},\n      encryptionKey: this.encryptionKey,\n    }\n\n    function mergeActionIds(\n      actionEntries: ActionEntries,\n      other: ActionEntries\n    ): void {\n      for (const key in other) {\n        const action = (actionEntries[key] ??= {\n          workers: {},\n          layer: {},\n        })\n        Object.assign(action.workers, other[key].workers)\n        Object.assign(action.layer, other[key].layer)\n      }\n    }\n\n    for (const m of manifests) {\n      mergeActionIds(manifest.node, m.node)\n      mergeActionIds(manifest.edge, m.edge)\n    }\n    for (const key in manifest.node) {\n      const entry = manifest.node[key]\n      entry.workers = sortObjectByKey(entry.workers)\n      entry.layer = sortObjectByKey(entry.layer)\n    }\n    for (const key in manifest.edge) {\n      const entry = manifest.edge[key]\n      entry.workers = sortObjectByKey(entry.workers)\n      entry.layer = sortObjectByKey(entry.layer)\n    }\n\n    return manifest\n  }\n\n  private async writeActionManifest(): Promise<void> {\n    const actionManifest = await this.mergeActionManifests(\n      this.actionManifests.values()\n    )\n    const actionManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.json`\n    )\n    const actionManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.js`\n    )\n    const json = JSON.stringify(actionManifest, null, 2)\n    deleteCache(actionManifestJsonPath)\n    deleteCache(actionManifestJsPath)\n    await writeFileAtomic(actionManifestJsonPath, json)\n    await writeFileAtomic(\n      actionManifestJsPath,\n      `self.__RSC_SERVER_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  async loadAppBuildManifest(pageName: string): Promise<void> {\n    this.appBuildManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_BUILD_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private mergeAppBuildManifests(manifests: Iterable<AppBuildManifest>) {\n    const manifest: AppBuildManifest = {\n      pages: {},\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n    }\n    manifest.pages = sortObjectByKey(manifest.pages)\n    return manifest\n  }\n\n  private async writeAppBuildManifest(): Promise<void> {\n    const appBuildManifest = this.mergeAppBuildManifests(\n      this.appBuildManifests.values()\n    )\n    const appBuildManifestPath = join(this.distDir, APP_BUILD_MANIFEST)\n    deleteCache(appBuildManifestPath)\n    await writeFileAtomic(\n      appBuildManifestPath,\n      JSON.stringify(appBuildManifest, null, 2)\n    )\n  }\n\n  async loadAppPathsManifest(pageName: string): Promise<void> {\n    this.appPathsManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_PATHS_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async writeAppPathsManifest(): Promise<void> {\n    const appPathsManifest = this.mergePagesManifests(\n      this.appPathsManifests.values()\n    )\n    const appPathsManifestPath = join(\n      this.distDir,\n      'server',\n      APP_PATHS_MANIFEST\n    )\n    deleteCache(appPathsManifestPath)\n    await writeFileAtomic(\n      appPathsManifestPath,\n      JSON.stringify(appPathsManifest, null, 2)\n    )\n  }\n\n  private async writeWebpackStats(): Promise<void> {\n    const webpackStats = this.mergeWebpackStats(this.webpackStats.values())\n    const path = join(this.distDir, 'server', WEBPACK_STATS)\n    deleteCache(path)\n    await writeFileAtomic(path, JSON.stringify(webpackStats, null, 2))\n  }\n\n  async loadBuildManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.buildManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(this.distDir, BUILD_MANIFEST, pageName, type)\n    )\n  }\n\n  async loadWebpackStats(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.webpackStats.set(\n      getEntryKey(type, 'client', pageName),\n      await readPartialManifest(this.distDir, WEBPACK_STATS, pageName, type)\n    )\n  }\n\n  private mergeWebpackStats(statsFiles: Iterable<WebpackStats>): WebpackStats {\n    const entrypoints: Record<string, StatsChunkGroup> = {}\n    const assets: Map<string, StatsAsset> = new Map()\n    const chunks: Map<string, StatsChunk> = new Map()\n    const modules: Map<string | number, StatsModule> = new Map()\n\n    for (const statsFile of statsFiles) {\n      if (statsFile.entrypoints) {\n        for (const [k, v] of Object.entries(statsFile.entrypoints)) {\n          if (!entrypoints[k]) {\n            entrypoints[k] = v\n          }\n        }\n      }\n\n      if (statsFile.assets) {\n        for (const asset of statsFile.assets) {\n          if (!assets.has(asset.name)) {\n            assets.set(asset.name, asset)\n          }\n        }\n      }\n\n      if (statsFile.chunks) {\n        for (const chunk of statsFile.chunks) {\n          if (!chunks.has(chunk.name)) {\n            chunks.set(chunk.name, chunk)\n          }\n        }\n      }\n\n      if (statsFile.modules) {\n        for (const module of statsFile.modules) {\n          const id = module.id\n          if (id != null) {\n            // Merge the chunk list for the module. This can vary across endpoints.\n            const existing = modules.get(id)\n            if (existing == null) {\n              modules.set(id, module)\n            } else if (module.chunks != null && existing.chunks != null) {\n              for (const chunk of module.chunks) {\n                if (!existing.chunks.includes(chunk)) {\n                  existing.chunks.push(chunk)\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return {\n      entrypoints,\n      assets: [...assets.values()],\n      chunks: [...chunks.values()],\n      modules: [...modules.values()],\n    }\n  }\n\n  private mergeBuildManifests(manifests: Iterable<BuildManifest>) {\n    const manifest: Partial<BuildManifest> & Pick<BuildManifest, 'pages'> = {\n      pages: {\n        '/_app': [],\n      },\n      // Something in next.js depends on these to exist even for app dir rendering\n      devFiles: [],\n      ampDevFiles: [],\n      polyfillFiles: [],\n      lowPriorityFiles: [\n        `static/${this.buildId}/_ssgManifest.js`,\n        `static/${this.buildId}/_buildManifest.js`,\n      ],\n      rootMainFiles: [],\n      ampFirstPages: [],\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n      if (m.rootMainFiles.length) manifest.rootMainFiles = m.rootMainFiles\n      // polyfillFiles should always be the same, so we can overwrite instead of actually merging\n      if (m.polyfillFiles.length) manifest.polyfillFiles = m.polyfillFiles\n    }\n    manifest.pages = sortObjectByKey(manifest.pages) as BuildManifest['pages']\n    return manifest\n  }\n\n  private async writeBuildManifest(\n    entrypoints: Entrypoints,\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined,\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n  ): Promise<void> {\n    const rewrites = productionRewrites ?? {\n      ...devRewrites,\n      beforeFiles: (devRewrites?.beforeFiles ?? []).map(processRoute),\n      afterFiles: (devRewrites?.afterFiles ?? []).map(processRoute),\n      fallback: (devRewrites?.fallback ?? []).map(processRoute),\n    }\n    const buildManifest = this.mergeBuildManifests(this.buildManifests.values())\n    const buildManifestPath = join(this.distDir, BUILD_MANIFEST)\n    const middlewareBuildManifestPath = join(\n      this.distDir,\n      'server',\n      `${MIDDLEWARE_BUILD_MANIFEST}.js`\n    )\n    const interceptionRewriteManifestPath = join(\n      this.distDir,\n      'server',\n      `${INTERCEPTION_ROUTE_REWRITE_MANIFEST}.js`\n    )\n    deleteCache(buildManifestPath)\n    deleteCache(middlewareBuildManifestPath)\n    deleteCache(interceptionRewriteManifestPath)\n    await writeFileAtomic(\n      buildManifestPath,\n      JSON.stringify(buildManifest, null, 2)\n    )\n    await writeFileAtomic(\n      middlewareBuildManifestPath,\n      // we use globalThis here because middleware can be node\n      // which doesn't have \"self\"\n      createEdgeRuntimeManifest(buildManifest)\n    )\n\n    const interceptionRewrites = JSON.stringify(\n      rewrites.beforeFiles.filter(isInterceptionRouteRewrite)\n    )\n\n    await writeFileAtomic(\n      interceptionRewriteManifestPath,\n      `self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST=${JSON.stringify(\n        interceptionRewrites\n      )};`\n    )\n\n    const pagesKeys = [...entrypoints.page.keys()]\n    if (entrypoints.global.app) {\n      pagesKeys.push('/_app')\n    }\n    if (entrypoints.global.error) {\n      pagesKeys.push('/_error')\n    }\n\n    const sortedPageKeys = getSortedRoutes(pagesKeys)\n    const content: ClientBuildManifest = {\n      __rewrites: normalizeRewritesForBuildManifest(rewrites) as any,\n      ...Object.fromEntries(\n        sortedPageKeys.map((pathname) => [\n          pathname,\n          [`static/chunks/pages${pathname === '/' ? '/index' : pathname}.js`],\n        ])\n      ),\n      sortedPages: sortedPageKeys,\n    }\n    const buildManifestJs = `self.__BUILD_MANIFEST = ${JSON.stringify(\n      content\n    )};self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()`\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_buildManifest.js'),\n      buildManifestJs\n    )\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_ssgManifest.js'),\n      srcEmptySsgManifest\n    )\n  }\n\n  private async writeClientMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    const matchers = middlewareManifest?.middleware['/']?.matchers || []\n\n    const clientMiddlewareManifestPath = join(\n      this.distDir,\n      'static',\n      this.buildId,\n      `${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n    )\n    deleteCache(clientMiddlewareManifestPath)\n    await writeFileAtomic(\n      clientMiddlewareManifestPath,\n      JSON.stringify(matchers, null, 2)\n    )\n  }\n\n  private async writeFallbackBuildManifest(): Promise<void> {\n    const fallbackBuildManifest = this.mergeBuildManifests(\n      [\n        this.buildManifests.get(getEntryKey('pages', 'server', '_app')),\n        this.buildManifests.get(getEntryKey('pages', 'server', '_error')),\n      ].filter(Boolean) as BuildManifest[]\n    )\n    const fallbackBuildManifestPath = join(\n      this.distDir,\n      `fallback-${BUILD_MANIFEST}`\n    )\n    deleteCache(fallbackBuildManifestPath)\n    await writeFileAtomic(\n      fallbackBuildManifestPath,\n      JSON.stringify(fallbackBuildManifest, null, 2)\n    )\n  }\n\n  async loadFontManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.fontManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${NEXT_FONT_MANIFEST}.json`,\n        pageName,\n        type\n      )\n    )\n  }\n\n  private mergeFontManifests(manifests: Iterable<NextFontManifest>) {\n    const manifest: NextFontManifest = {\n      app: {},\n      appUsingSizeAdjust: false,\n      pages: {},\n      pagesUsingSizeAdjust: false,\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.app, m.app)\n      Object.assign(manifest.pages, m.pages)\n\n      manifest.appUsingSizeAdjust =\n        manifest.appUsingSizeAdjust || m.appUsingSizeAdjust\n      manifest.pagesUsingSizeAdjust =\n        manifest.pagesUsingSizeAdjust || m.pagesUsingSizeAdjust\n    }\n    manifest.app = sortObjectByKey(manifest.app)\n    manifest.pages = sortObjectByKey(manifest.pages)\n    return manifest\n  }\n\n  private async writeNextFontManifest(): Promise<void> {\n    const fontManifest = this.mergeFontManifests(this.fontManifests.values())\n    const json = JSON.stringify(fontManifest, null, 2)\n\n    const fontManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.json`\n    )\n    const fontManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.js`\n    )\n    deleteCache(fontManifestJsonPath)\n    deleteCache(fontManifestJsPath)\n    await writeFileAtomic(fontManifestJsonPath, json)\n    await writeFileAtomic(\n      fontManifestJsPath,\n      `self.__NEXT_FONT_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  /**\n   * @returns If the manifest was written or not\n   */\n  async loadMiddlewareManifest(\n    pageName: string,\n    type: 'pages' | 'app' | 'middleware' | 'instrumentation'\n  ): Promise<boolean> {\n    const middlewareManifestPath = getManifestPath(\n      pageName,\n      this.distDir,\n      MIDDLEWARE_MANIFEST,\n      type,\n      true\n    )\n\n    // middlewareManifest is actually \"edge manifest\" and not all routes are edge runtime. If it is not written we skip it.\n    if (!existsSync(middlewareManifestPath)) {\n      return false\n    }\n\n    this.middlewareManifests.set(\n      getEntryKey(\n        type === 'middleware' || type === 'instrumentation' ? 'root' : type,\n        'server',\n        pageName\n      ),\n      await readPartialManifest(\n        this.distDir,\n        MIDDLEWARE_MANIFEST,\n        pageName,\n        type\n      )\n    )\n\n    return true\n  }\n\n  getMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.get(key)\n  }\n\n  deleteMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.delete(key)\n  }\n\n  private mergeMiddlewareManifests(\n    manifests: Iterable<TurbopackMiddlewareManifest>\n  ): MiddlewareManifest {\n    const manifest: MiddlewareManifest = {\n      version: 3,\n      middleware: {},\n      sortedMiddleware: [],\n      functions: {},\n    }\n    let instrumentation: InstrumentationDefinition | undefined = undefined\n    for (const m of manifests) {\n      Object.assign(manifest.functions, m.functions)\n      Object.assign(manifest.middleware, m.middleware)\n      if (m.instrumentation) {\n        instrumentation = m.instrumentation\n      }\n    }\n    manifest.functions = sortObjectByKey(manifest.functions)\n    manifest.middleware = sortObjectByKey(manifest.middleware)\n    const updateFunctionDefinition = (\n      fun: EdgeFunctionDefinition\n    ): EdgeFunctionDefinition => {\n      return {\n        ...fun,\n        files: [...(instrumentation?.files ?? []), ...fun.files],\n      }\n    }\n    for (const key of Object.keys(manifest.middleware)) {\n      const value = manifest.middleware[key]\n      manifest.middleware[key] = updateFunctionDefinition(value)\n    }\n    for (const key of Object.keys(manifest.functions)) {\n      const value = manifest.functions[key]\n      manifest.functions[key] = updateFunctionDefinition(value)\n    }\n    for (const fun of Object.values(manifest.functions).concat(\n      Object.values(manifest.middleware)\n    )) {\n      for (const matcher of fun.matchers) {\n        if (!matcher.regexp) {\n          matcher.regexp = pathToRegexp(matcher.originalSource, [], {\n            delimiter: '/',\n            sensitive: false,\n            strict: true,\n          }).source.replaceAll('\\\\/', '/')\n        }\n      }\n    }\n    manifest.sortedMiddleware = Object.keys(manifest.middleware)\n\n    return manifest\n  }\n\n  private async writeMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    // Normalize regexes as it uses path-to-regexp\n    for (const key in middlewareManifest.middleware) {\n      middlewareManifest.middleware[key].matchers.forEach((matcher) => {\n        if (!matcher.regexp.startsWith('^')) {\n          const parsedPage = tryToParsePath(matcher.regexp)\n          if (parsedPage.error || !parsedPage.regexStr) {\n            throw new Error(`Invalid source: ${matcher.regexp}`)\n          }\n          matcher.regexp = parsedPage.regexStr\n        }\n      })\n    }\n\n    const middlewareManifestPath = join(\n      this.distDir,\n      'server',\n      MIDDLEWARE_MANIFEST\n    )\n    deleteCache(middlewareManifestPath)\n    await writeFileAtomic(\n      middlewareManifestPath,\n      JSON.stringify(middlewareManifest, null, 2)\n    )\n  }\n\n  async loadPagesManifest(pageName: string): Promise<void> {\n    this.pagesManifests.set(\n      getEntryKey('pages', 'server', pageName),\n      await readPartialManifest(this.distDir, PAGES_MANIFEST, pageName)\n    )\n  }\n\n  private mergePagesManifests(manifests: Iterable<PagesManifest>) {\n    const manifest: PagesManifest = {}\n    for (const m of manifests) {\n      Object.assign(manifest, m)\n    }\n    return sortObjectByKey(manifest)\n  }\n\n  private async writePagesManifest(): Promise<void> {\n    const pagesManifest = this.mergePagesManifests(this.pagesManifests.values())\n    const pagesManifestPath = join(this.distDir, 'server', PAGES_MANIFEST)\n    deleteCache(pagesManifestPath)\n    await writeFileAtomic(\n      pagesManifestPath,\n      JSON.stringify(pagesManifest, null, 2)\n    )\n  }\n\n  async writeManifests({\n    devRewrites,\n    productionRewrites,\n    entrypoints,\n  }: {\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n    entrypoints: Entrypoints\n  }) {\n    await this.writeActionManifest()\n    await this.writeAppBuildManifest()\n    await this.writeAppPathsManifest()\n    await this.writeBuildManifest(entrypoints, devRewrites, productionRewrites)\n    await this.writeFallbackBuildManifest()\n    await this.writeMiddlewareManifest()\n    await this.writeClientMiddlewareManifest()\n    await this.writeNextFontManifest()\n    await this.writePagesManifest()\n\n    if (process.env.TURBOPACK_STATS != null) {\n      await this.writeWebpackStats()\n    }\n  }\n}\n\nfunction sortObjectByKey(obj: Record<string, any>) {\n  return Object.keys(obj)\n    .sort()\n    .reduce(\n      (acc, key) => {\n        acc[key] = obj[key]\n        return acc\n      },\n      {} as Record<string, any>\n    )\n}\n"], "names": ["pathToRegexp", "APP_BUILD_MANIFEST", "APP_PATHS_MANIFEST", "BUILD_MANIFEST", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_MANIFEST", "NEXT_FONT_MANIFEST", "PAGES_MANIFEST", "SERVER_REFERENCE_MANIFEST", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "WEBPACK_STATS", "join", "posix", "readFile", "deleteCache", "writeFileAtomic", "isInterceptionRouteRewrite", "normalizeRewritesForBuildManifest", "srcEmptySsgManifest", "processRoute", "createEdgeRuntimeManifest", "getAssetPathFromRoute", "getEntry<PERSON>ey", "getSortedRoutes", "existsSync", "addMetadataIdToRoute", "addRouteSuffix", "removeRouteSuffix", "tryToParsePath", "getManifestPath", "page", "distDir", "name", "type", "firstCall", "manifestPath", "isSitemapRoute", "test", "replace", "endsWith", "metadataPage", "readPartialManifest", "pageName", "JSON", "parse", "TurbopackManifestLoader", "delete", "key", "actionManifests", "appBuildManifests", "appPathsManifests", "buildManifests", "fontManifests", "middlewareManifests", "pagesManifests", "webpackStats", "loadActionManifest", "set", "mergeActionManifests", "manifests", "manifest", "node", "edge", "<PERSON><PERSON><PERSON>", "mergeActionIds", "actionEntries", "other", "action", "workers", "layer", "Object", "assign", "m", "entry", "sortObjectByKey", "writeActionManifest", "actionManifest", "values", "actionManifestJsonPath", "actionManifestJsPath", "json", "stringify", "loadAppBuildManifest", "mergeAppBuildManifests", "pages", "writeAppBuildManifest", "appBuildManifest", "appBuildManifestPath", "loadAppPathsManifest", "writeAppPathsManifest", "appPathsManifest", "mergePagesManifests", "appPathsManifestPath", "writeWebpackStats", "mergeWebpackStats", "path", "loadBuildManifest", "loadWebpackStats", "statsFiles", "entrypoints", "assets", "Map", "chunks", "modules", "statsFile", "k", "v", "entries", "asset", "has", "chunk", "module", "id", "existing", "get", "includes", "push", "mergeBuildManifests", "devFiles", "ampDevFiles", "polyfillFiles", "lowPriorityFiles", "buildId", "rootMainFiles", "ampFirstPages", "length", "writeBuildManifest", "devRewrites", "productionRewrites", "rewrites", "beforeFiles", "map", "afterFiles", "fallback", "buildManifest", "buildManifestPath", "middlewareBuildManifestPath", "interceptionRewriteManifestPath", "interceptionRewrites", "filter", "pagesKeys", "keys", "global", "app", "error", "sortedPageKeys", "content", "__rewrites", "fromEntries", "pathname", "sortedPages", "buildManifestJs", "writeClientMiddlewareManifest", "middlewareManifest", "mergeMiddlewareManifests", "matchers", "middleware", "clientMiddlewareManifestPath", "writeFallbackBuildManifest", "fallbackBuildManifest", "Boolean", "fallbackBuildManifestPath", "loadFontManifest", "mergeFontManifests", "appUsingSizeAdjust", "pagesUsingSizeAdjust", "writeNextFontManifest", "fontManifest", "fontManifestJsonPath", "fontManifestJsPath", "loadMiddlewareManifest", "middlewareManifestPath", "getMiddlewareManifest", "deleteMiddlewareManifest", "version", "sortedMiddleware", "functions", "instrumentation", "undefined", "updateFunctionDefinition", "fun", "files", "value", "concat", "matcher", "regexp", "originalSource", "delimiter", "sensitive", "strict", "source", "replaceAll", "writeMiddlewareManifest", "for<PERSON>ach", "startsWith", "parsedPage", "regexStr", "Error", "loadPagesManifest", "writePagesManifest", "pagesManifest", "pagesManifestPath", "writeManifests", "process", "env", "TURBOPACK_STATS", "constructor", "obj", "sort", "reduce", "acc"], "mappings": "AAcA,SAASA,YAAY,QAAQ,oCAAmC;AAIhE,SACEC,kBAAkB,EAClBC,kBAAkB,EAClBC,cAAc,EACdC,mCAAmC,EACnCC,yBAAyB,EACzBC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,yBAAyB,EACzBC,oCAAoC,EACpCC,aAAa,QACR,eAAc;AACrB,SAASC,IAAI,EAAEC,KAAK,QAAQ,OAAM;AAClC,SAASC,QAAQ,QAAQ,cAAa;AAEtC,SAASC,WAAW,QAAQ,oCAAmC;AAC/D,SAASC,eAAe,QAAQ,+BAA8B;AAC9D,SAASC,0BAA0B,QAAQ,qDAAoD;AAC/F,SAEEC,iCAAiC,EACjCC,mBAAmB,EACnBC,YAAY,EACZC,yBAAyB,QACpB,uDAAsD;AAC7D,OAAOC,2BAA2B,4CAA2C;AAC7E,SAASC,WAAW,QAAuB,cAAa;AAExD,SAASC,eAAe,QAAQ,kBAAiB;AACjD,SAASC,UAAU,QAAQ,KAAI;AAC/B,SACEC,oBAAoB,EACpBC,cAAc,EACdC,iBAAiB,QACZ,sCAAqC;AAC5C,SAASC,cAAc,QAAQ,iCAAgC;AAuB/D,MAAMC,kBAAkB,CACtBC,MACAC,SACAC,MACAC,MACAC;IAEA,IAAIC,eAAevB,MAAMD,IAAI,CAC3BoB,SACC,UACDE,MACAA,SAAS,gBAAgBA,SAAS,oBAC9B,KACAA,SAAS,QACPH,OACAT,sBAAsBS,OAC5BE;IAGF,IAAIE,WAAW;QACb,MAAME,iBAAiB,8BAA8BC,IAAI,CAACP;QAC1D,mDAAmD;QACnD,IAAIM,kBAAkB,CAACZ,WAAWW,eAAe;YAC/CA,eAAeN,gBACbC,KAAKQ,OAAO,CAAC,qBAAqB,uBAClCP,SACAC,MACAC,MACA;QAEJ;QACA,oDAAoD;QACpD,IAAI,CAACT,WAAWW,iBAAiBL,KAAKS,QAAQ,CAAC,WAAW;YACxD,6IAA6I;YAC7I,IAAIC,eAAed,eACjBD,qBAAqBE,kBAAkBG;YAEzCK,eAAeN,gBAAgBW,cAAcT,SAASC,MAAMC,MAAM;QACpE;IACF;IAEA,OAAOE;AACT;AAEA,eAAeM,oBACbV,OAAe,EACfC,IAAkB,EAClBU,QAAgB,EAChBT,IAAkE;IAAlEA,IAAAA,iBAAAA,OAA2D;IAE3D,MAAMH,OAAOY;IACb,MAAMP,eAAeN,gBAAgBC,MAAMC,SAASC,MAAMC,MAAM;IAChE,OAAOU,KAAKC,KAAK,CAAC,MAAM/B,SAASD,MAAMD,IAAI,CAACwB,eAAe;AAC7D;AAEA,OAAO,MAAMU;IA6BXC,OAAOC,GAAa,EAAE;QACpB,IAAI,CAACC,eAAe,CAACF,MAAM,CAACC;QAC5B,IAAI,CAACE,iBAAiB,CAACH,MAAM,CAACC;QAC9B,IAAI,CAACG,iBAAiB,CAACJ,MAAM,CAACC;QAC9B,IAAI,CAACI,cAAc,CAACL,MAAM,CAACC;QAC3B,IAAI,CAACK,aAAa,CAACN,MAAM,CAACC;QAC1B,IAAI,CAACM,mBAAmB,CAACP,MAAM,CAACC;QAChC,IAAI,CAACO,cAAc,CAACR,MAAM,CAACC;QAC3B,IAAI,CAACQ,YAAY,CAACT,MAAM,CAACC;IAC3B;IAEA,MAAMS,mBAAmBd,QAAgB,EAAiB;QACxD,IAAI,CAACM,eAAe,CAACS,GAAG,CACtBnC,YAAY,OAAO,UAAUoB,WAC7B,MAAMD,oBACJ,IAAI,CAACV,OAAO,EACZ,AAAC,KAAEvB,4BAA0B,SAC7BkC,UACA;IAGN;IAEA,MAAcgB,qBAAqBC,SAAmC,EAAE;QAEtE,MAAMC,WAA2B;YAC/BC,MAAM,CAAC;YACPC,MAAM,CAAC;YACPC,eAAe,IAAI,CAACA,aAAa;QACnC;QAEA,SAASC,eACPC,aAA4B,EAC5BC,KAAoB;YAEpB,IAAK,MAAMnB,OAAOmB,MAAO;oBACPD,gBAAclB;;gBAA9B,MAAMoB,SAAUF,MAAAA,iBAAAA,cAAa,CAAClB,OAAAA,IAAI,gBAAlBkB,cAAa,CAAClB,KAAI,GAAK;oBACrCqB,SAAS,CAAC;oBACVC,OAAO,CAAC;gBACV;gBACAC,OAAOC,MAAM,CAACJ,OAAOC,OAAO,EAAEF,KAAK,CAACnB,IAAI,CAACqB,OAAO;gBAChDE,OAAOC,MAAM,CAACJ,OAAOE,KAAK,EAAEH,KAAK,CAACnB,IAAI,CAACsB,KAAK;YAC9C;QACF;QAEA,KAAK,MAAMG,KAAKb,UAAW;YACzBK,eAAeJ,SAASC,IAAI,EAAEW,EAAEX,IAAI;YACpCG,eAAeJ,SAASE,IAAI,EAAEU,EAAEV,IAAI;QACtC;QACA,IAAK,MAAMf,OAAOa,SAASC,IAAI,CAAE;YAC/B,MAAMY,QAAQb,SAASC,IAAI,CAACd,IAAI;YAChC0B,MAAML,OAAO,GAAGM,gBAAgBD,MAAML,OAAO;YAC7CK,MAAMJ,KAAK,GAAGK,gBAAgBD,MAAMJ,KAAK;QAC3C;QACA,IAAK,MAAMtB,OAAOa,SAASE,IAAI,CAAE;YAC/B,MAAMW,QAAQb,SAASE,IAAI,CAACf,IAAI;YAChC0B,MAAML,OAAO,GAAGM,gBAAgBD,MAAML,OAAO;YAC7CK,MAAMJ,KAAK,GAAGK,gBAAgBD,MAAMJ,KAAK;QAC3C;QAEA,OAAOT;IACT;IAEA,MAAce,sBAAqC;QACjD,MAAMC,iBAAiB,MAAM,IAAI,CAAClB,oBAAoB,CACpD,IAAI,CAACV,eAAe,CAAC6B,MAAM;QAE7B,MAAMC,yBAAyBnE,KAC7B,IAAI,CAACoB,OAAO,EACZ,UACA,AAAC,KAAEvB,4BAA0B;QAE/B,MAAMuE,uBAAuBpE,KAC3B,IAAI,CAACoB,OAAO,EACZ,UACA,AAAC,KAAEvB,4BAA0B;QAE/B,MAAMwE,OAAOrC,KAAKsC,SAAS,CAACL,gBAAgB,MAAM;QAClD9D,YAAYgE;QACZhE,YAAYiE;QACZ,MAAMhE,gBAAgB+D,wBAAwBE;QAC9C,MAAMjE,gBACJgE,sBACA,AAAC,gCAA6BpC,KAAKsC,SAAS,CAACD;IAEjD;IAEA,MAAME,qBAAqBxC,QAAgB,EAAiB;QAC1D,IAAI,CAACO,iBAAiB,CAACQ,GAAG,CACxBnC,YAAY,OAAO,UAAUoB,WAC7B,MAAMD,oBACJ,IAAI,CAACV,OAAO,EACZ/B,oBACA0C,UACA;IAGN;IAEQyC,uBAAuBxB,SAAqC,EAAE;QACpE,MAAMC,WAA6B;YACjCwB,OAAO,CAAC;QACV;QACA,KAAK,MAAMZ,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAASwB,KAAK,EAAEZ,EAAEY,KAAK;QACvC;QACAxB,SAASwB,KAAK,GAAGV,gBAAgBd,SAASwB,KAAK;QAC/C,OAAOxB;IACT;IAEA,MAAcyB,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACH,sBAAsB,CAClD,IAAI,CAAClC,iBAAiB,CAAC4B,MAAM;QAE/B,MAAMU,uBAAuB5E,KAAK,IAAI,CAACoB,OAAO,EAAE/B;QAChDc,YAAYyE;QACZ,MAAMxE,gBACJwE,sBACA5C,KAAKsC,SAAS,CAACK,kBAAkB,MAAM;IAE3C;IAEA,MAAME,qBAAqB9C,QAAgB,EAAiB;QAC1D,IAAI,CAACQ,iBAAiB,CAACO,GAAG,CACxBnC,YAAY,OAAO,UAAUoB,WAC7B,MAAMD,oBACJ,IAAI,CAACV,OAAO,EACZ9B,oBACAyC,UACA;IAGN;IAEA,MAAc+C,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACC,mBAAmB,CAC/C,IAAI,CAACzC,iBAAiB,CAAC2B,MAAM;QAE/B,MAAMe,uBAAuBjF,KAC3B,IAAI,CAACoB,OAAO,EACZ,UACA9B;QAEFa,YAAY8E;QACZ,MAAM7E,gBACJ6E,sBACAjD,KAAKsC,SAAS,CAACS,kBAAkB,MAAM;IAE3C;IAEA,MAAcG,oBAAmC;QAC/C,MAAMtC,eAAe,IAAI,CAACuC,iBAAiB,CAAC,IAAI,CAACvC,YAAY,CAACsB,MAAM;QACpE,MAAMkB,OAAOpF,KAAK,IAAI,CAACoB,OAAO,EAAE,UAAUrB;QAC1CI,YAAYiF;QACZ,MAAMhF,gBAAgBgF,MAAMpD,KAAKsC,SAAS,CAAC1B,cAAc,MAAM;IACjE;IAEA,MAAMyC,kBACJtD,QAAgB,EAChBT,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAACkB,cAAc,CAACM,GAAG,CACrBnC,YAAYW,MAAM,UAAUS,WAC5B,MAAMD,oBAAoB,IAAI,CAACV,OAAO,EAAE7B,gBAAgBwC,UAAUT;IAEtE;IAEA,MAAMgE,iBACJvD,QAAgB,EAChBT,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAACsB,YAAY,CAACE,GAAG,CACnBnC,YAAYW,MAAM,UAAUS,WAC5B,MAAMD,oBAAoB,IAAI,CAACV,OAAO,EAAErB,eAAegC,UAAUT;IAErE;IAEQ6D,kBAAkBI,UAAkC,EAAgB;QAC1E,MAAMC,cAA+C,CAAC;QACtD,MAAMC,SAAkC,IAAIC;QAC5C,MAAMC,SAAkC,IAAID;QAC5C,MAAME,UAA6C,IAAIF;QAEvD,KAAK,MAAMG,aAAaN,WAAY;YAClC,IAAIM,UAAUL,WAAW,EAAE;gBACzB,KAAK,MAAM,CAACM,GAAGC,EAAE,IAAIpC,OAAOqC,OAAO,CAACH,UAAUL,WAAW,EAAG;oBAC1D,IAAI,CAACA,WAAW,CAACM,EAAE,EAAE;wBACnBN,WAAW,CAACM,EAAE,GAAGC;oBACnB;gBACF;YACF;YAEA,IAAIF,UAAUJ,MAAM,EAAE;gBACpB,KAAK,MAAMQ,SAASJ,UAAUJ,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOS,GAAG,CAACD,MAAM5E,IAAI,GAAG;wBAC3BoE,OAAO3C,GAAG,CAACmD,MAAM5E,IAAI,EAAE4E;oBACzB;gBACF;YACF;YAEA,IAAIJ,UAAUF,MAAM,EAAE;gBACpB,KAAK,MAAMQ,SAASN,UAAUF,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOO,GAAG,CAACC,MAAM9E,IAAI,GAAG;wBAC3BsE,OAAO7C,GAAG,CAACqD,MAAM9E,IAAI,EAAE8E;oBACzB;gBACF;YACF;YAEA,IAAIN,UAAUD,OAAO,EAAE;gBACrB,KAAK,MAAMQ,UAAUP,UAAUD,OAAO,CAAE;oBACtC,MAAMS,KAAKD,OAAOC,EAAE;oBACpB,IAAIA,MAAM,MAAM;wBACd,uEAAuE;wBACvE,MAAMC,WAAWV,QAAQW,GAAG,CAACF;wBAC7B,IAAIC,YAAY,MAAM;4BACpBV,QAAQ9C,GAAG,CAACuD,IAAID;wBAClB,OAAO,IAAIA,OAAOT,MAAM,IAAI,QAAQW,SAASX,MAAM,IAAI,MAAM;4BAC3D,KAAK,MAAMQ,SAASC,OAAOT,MAAM,CAAE;gCACjC,IAAI,CAACW,SAASX,MAAM,CAACa,QAAQ,CAACL,QAAQ;oCACpCG,SAASX,MAAM,CAACc,IAAI,CAACN;gCACvB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YACLX;YACAC,QAAQ;mBAAIA,OAAOvB,MAAM;aAAG;YAC5ByB,QAAQ;mBAAIA,OAAOzB,MAAM;aAAG;YAC5B0B,SAAS;mBAAIA,QAAQ1B,MAAM;aAAG;QAChC;IACF;IAEQwC,oBAAoB1D,SAAkC,EAAE;QAC9D,MAAMC,WAAkE;YACtEwB,OAAO;gBACL,SAAS,EAAE;YACb;YACA,4EAA4E;YAC5EkC,UAAU,EAAE;YACZC,aAAa,EAAE;YACfC,eAAe,EAAE;YACjBC,kBAAkB;gBACf,YAAS,IAAI,CAACC,OAAO,GAAC;gBACtB,YAAS,IAAI,CAACA,OAAO,GAAC;aACxB;YACDC,eAAe,EAAE;YACjBC,eAAe,EAAE;QACnB;QACA,KAAK,MAAMpD,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAASwB,KAAK,EAAEZ,EAAEY,KAAK;YACrC,IAAIZ,EAAEmD,aAAa,CAACE,MAAM,EAAEjE,SAAS+D,aAAa,GAAGnD,EAAEmD,aAAa;YACpE,2FAA2F;YAC3F,IAAInD,EAAEgD,aAAa,CAACK,MAAM,EAAEjE,SAAS4D,aAAa,GAAGhD,EAAEgD,aAAa;QACtE;QACA5D,SAASwB,KAAK,GAAGV,gBAAgBd,SAASwB,KAAK;QAC/C,OAAOxB;IACT;IAEA,MAAckE,mBACZ3B,WAAwB,EACxB4B,WAA2D,EAC3DC,kBAAwD,EACzC;YAGCD,0BACDA,yBACFA;QAJb,MAAME,WAAWD,6BAAAA,qBAAsB;YACrC,GAAGD,WAAW;YACdG,aAAa,AAACH,CAAAA,CAAAA,2BAAAA,+BAAAA,YAAaG,WAAW,YAAxBH,2BAA4B,EAAE,AAAD,EAAGI,GAAG,CAAChH;YAClDiH,YAAY,AAACL,CAAAA,CAAAA,0BAAAA,+BAAAA,YAAaK,UAAU,YAAvBL,0BAA2B,EAAE,AAAD,EAAGI,GAAG,CAAChH;YAChDkH,UAAU,AAACN,CAAAA,CAAAA,wBAAAA,+BAAAA,YAAaM,QAAQ,YAArBN,wBAAyB,EAAE,AAAD,EAAGI,GAAG,CAAChH;QAC9C;QACA,MAAMmH,gBAAgB,IAAI,CAACjB,mBAAmB,CAAC,IAAI,CAAClE,cAAc,CAAC0B,MAAM;QACzE,MAAM0D,oBAAoB5H,KAAK,IAAI,CAACoB,OAAO,EAAE7B;QAC7C,MAAMsI,8BAA8B7H,KAClC,IAAI,CAACoB,OAAO,EACZ,UACA,AAAC,KAAE3B,4BAA0B;QAE/B,MAAMqI,kCAAkC9H,KACtC,IAAI,CAACoB,OAAO,EACZ,UACA,AAAC,KAAE5B,sCAAoC;QAEzCW,YAAYyH;QACZzH,YAAY0H;QACZ1H,YAAY2H;QACZ,MAAM1H,gBACJwH,mBACA5F,KAAKsC,SAAS,CAACqD,eAAe,MAAM;QAEtC,MAAMvH,gBACJyH,6BACA,wDAAwD;QACxD,4BAA4B;QAC5BpH,0BAA0BkH;QAG5B,MAAMI,uBAAuB/F,KAAKsC,SAAS,CACzCgD,SAASC,WAAW,CAACS,MAAM,CAAC3H;QAG9B,MAAMD,gBACJ0H,iCACA,AAAC,gDAA6C9F,KAAKsC,SAAS,CAC1DyD,wBACA;QAGJ,MAAME,YAAY;eAAIzC,YAAYrE,IAAI,CAAC+G,IAAI;SAAG;QAC9C,IAAI1C,YAAY2C,MAAM,CAACC,GAAG,EAAE;YAC1BH,UAAUxB,IAAI,CAAC;QACjB;QACA,IAAIjB,YAAY2C,MAAM,CAACE,KAAK,EAAE;YAC5BJ,UAAUxB,IAAI,CAAC;QACjB;QAEA,MAAM6B,iBAAiB1H,gBAAgBqH;QACvC,MAAMM,UAA+B;YACnCC,YAAYlI,kCAAkCgH;YAC9C,GAAG3D,OAAO8E,WAAW,CACnBH,eAAed,GAAG,CAAC,CAACkB,WAAa;oBAC/BA;oBACA;wBAAE,wBAAqBA,CAAAA,aAAa,MAAM,WAAWA,QAAO,IAAE;qBAAK;iBACpE,EACF;YACDC,aAAaL;QACf;QACA,MAAMM,kBAAkB,AAAC,6BAA0B5G,KAAKsC,SAAS,CAC/DiE,WACA;QACF,MAAMnI,gBACJJ,KAAK,IAAI,CAACoB,OAAO,EAAE,UAAU,IAAI,CAAC2F,OAAO,EAAE,sBAC3C6B;QAEF,MAAMxI,gBACJJ,KAAK,IAAI,CAACoB,OAAO,EAAE,UAAU,IAAI,CAAC2F,OAAO,EAAE,oBAC3CxG;IAEJ;IAEA,MAAcsI,gCAA+C;YAK1CC;QAJjB,MAAMA,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAACrG,mBAAmB,CAACwB,MAAM;QAGjC,MAAM8E,WAAWF,CAAAA,uCAAAA,kCAAAA,mBAAoBG,UAAU,CAAC,IAAI,qBAAnCH,gCAAqCE,QAAQ,KAAI,EAAE;QAEpE,MAAME,+BAA+BlJ,KACnC,IAAI,CAACoB,OAAO,EACZ,UACA,IAAI,CAAC2F,OAAO,EACZ,AAAC,KAAEjH;QAELK,YAAY+I;QACZ,MAAM9I,gBACJ8I,8BACAlH,KAAKsC,SAAS,CAAC0E,UAAU,MAAM;IAEnC;IAEA,MAAcG,6BAA4C;QACxD,MAAMC,wBAAwB,IAAI,CAAC1C,mBAAmB,CACpD;YACE,IAAI,CAAClE,cAAc,CAAC+D,GAAG,CAAC5F,YAAY,SAAS,UAAU;YACvD,IAAI,CAAC6B,cAAc,CAAC+D,GAAG,CAAC5F,YAAY,SAAS,UAAU;SACxD,CAACqH,MAAM,CAACqB;QAEX,MAAMC,4BAA4BtJ,KAChC,IAAI,CAACoB,OAAO,EACZ,AAAC,cAAW7B;QAEdY,YAAYmJ;QACZ,MAAMlJ,gBACJkJ,2BACAtH,KAAKsC,SAAS,CAAC8E,uBAAuB,MAAM;IAEhD;IAEA,MAAMG,iBACJxH,QAAgB,EAChBT,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAACmB,aAAa,CAACK,GAAG,CACpBnC,YAAYW,MAAM,UAAUS,WAC5B,MAAMD,oBACJ,IAAI,CAACV,OAAO,EACZ,AAAC,KAAEzB,qBAAmB,SACtBoC,UACAT;IAGN;IAEQkI,mBAAmBxG,SAAqC,EAAE;QAChE,MAAMC,WAA6B;YACjCmF,KAAK,CAAC;YACNqB,oBAAoB;YACpBhF,OAAO,CAAC;YACRiF,sBAAsB;QACxB;QACA,KAAK,MAAM7F,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAASmF,GAAG,EAAEvE,EAAEuE,GAAG;YACjCzE,OAAOC,MAAM,CAACX,SAASwB,KAAK,EAAEZ,EAAEY,KAAK;YAErCxB,SAASwG,kBAAkB,GACzBxG,SAASwG,kBAAkB,IAAI5F,EAAE4F,kBAAkB;YACrDxG,SAASyG,oBAAoB,GAC3BzG,SAASyG,oBAAoB,IAAI7F,EAAE6F,oBAAoB;QAC3D;QACAzG,SAASmF,GAAG,GAAGrE,gBAAgBd,SAASmF,GAAG;QAC3CnF,SAASwB,KAAK,GAAGV,gBAAgBd,SAASwB,KAAK;QAC/C,OAAOxB;IACT;IAEA,MAAc0G,wBAAuC;QACnD,MAAMC,eAAe,IAAI,CAACJ,kBAAkB,CAAC,IAAI,CAAC/G,aAAa,CAACyB,MAAM;QACtE,MAAMG,OAAOrC,KAAKsC,SAAS,CAACsF,cAAc,MAAM;QAEhD,MAAMC,uBAAuB7J,KAC3B,IAAI,CAACoB,OAAO,EACZ,UACA,AAAC,KAAEzB,qBAAmB;QAExB,MAAMmK,qBAAqB9J,KACzB,IAAI,CAACoB,OAAO,EACZ,UACA,AAAC,KAAEzB,qBAAmB;QAExBQ,YAAY0J;QACZ1J,YAAY2J;QACZ,MAAM1J,gBAAgByJ,sBAAsBxF;QAC5C,MAAMjE,gBACJ0J,oBACA,AAAC,+BAA4B9H,KAAKsC,SAAS,CAACD;IAEhD;IAEA;;GAEC,GACD,MAAM0F,uBACJhI,QAAgB,EAChBT,IAAwD,EACtC;QAClB,MAAM0I,yBAAyB9I,gBAC7Ba,UACA,IAAI,CAACX,OAAO,EACZ1B,qBACA4B,MACA;QAGF,uHAAuH;QACvH,IAAI,CAACT,WAAWmJ,yBAAyB;YACvC,OAAO;QACT;QAEA,IAAI,CAACtH,mBAAmB,CAACI,GAAG,CAC1BnC,YACEW,SAAS,gBAAgBA,SAAS,oBAAoB,SAASA,MAC/D,UACAS,WAEF,MAAMD,oBACJ,IAAI,CAACV,OAAO,EACZ1B,qBACAqC,UACAT;QAIJ,OAAO;IACT;IAEA2I,sBAAsB7H,GAAa,EAAE;QACnC,OAAO,IAAI,CAACM,mBAAmB,CAAC6D,GAAG,CAACnE;IACtC;IAEA8H,yBAAyB9H,GAAa,EAAE;QACtC,OAAO,IAAI,CAACM,mBAAmB,CAACP,MAAM,CAACC;IACzC;IAEQ2G,yBACN/F,SAAgD,EAC5B;QACpB,MAAMC,WAA+B;YACnCkH,SAAS;YACTlB,YAAY,CAAC;YACbmB,kBAAkB,EAAE;YACpBC,WAAW,CAAC;QACd;QACA,IAAIC,kBAAyDC;QAC7D,KAAK,MAAM1G,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAASoH,SAAS,EAAExG,EAAEwG,SAAS;YAC7C1G,OAAOC,MAAM,CAACX,SAASgG,UAAU,EAAEpF,EAAEoF,UAAU;YAC/C,IAAIpF,EAAEyG,eAAe,EAAE;gBACrBA,kBAAkBzG,EAAEyG,eAAe;YACrC;QACF;QACArH,SAASoH,SAAS,GAAGtG,gBAAgBd,SAASoH,SAAS;QACvDpH,SAASgG,UAAU,GAAGlF,gBAAgBd,SAASgG,UAAU;QACzD,MAAMuB,2BAA2B,CAC/BC;gBAIcH;YAFd,OAAO;gBACL,GAAGG,GAAG;gBACNC,OAAO;uBAAKJ,CAAAA,yBAAAA,mCAAAA,gBAAiBI,KAAK,YAAtBJ,yBAA0B,EAAE;uBAAMG,IAAIC,KAAK;iBAAC;YAC1D;QACF;QACA,KAAK,MAAMtI,OAAOuB,OAAOuE,IAAI,CAACjF,SAASgG,UAAU,EAAG;YAClD,MAAM0B,QAAQ1H,SAASgG,UAAU,CAAC7G,IAAI;YACtCa,SAASgG,UAAU,CAAC7G,IAAI,GAAGoI,yBAAyBG;QACtD;QACA,KAAK,MAAMvI,OAAOuB,OAAOuE,IAAI,CAACjF,SAASoH,SAAS,EAAG;YACjD,MAAMM,QAAQ1H,SAASoH,SAAS,CAACjI,IAAI;YACrCa,SAASoH,SAAS,CAACjI,IAAI,GAAGoI,yBAAyBG;QACrD;QACA,KAAK,MAAMF,OAAO9G,OAAOO,MAAM,CAACjB,SAASoH,SAAS,EAAEO,MAAM,CACxDjH,OAAOO,MAAM,CAACjB,SAASgG,UAAU,GAChC;YACD,KAAK,MAAM4B,WAAWJ,IAAIzB,QAAQ,CAAE;gBAClC,IAAI,CAAC6B,QAAQC,MAAM,EAAE;oBACnBD,QAAQC,MAAM,GAAG1L,aAAayL,QAAQE,cAAc,EAAE,EAAE,EAAE;wBACxDC,WAAW;wBACXC,WAAW;wBACXC,QAAQ;oBACV,GAAGC,MAAM,CAACC,UAAU,CAAC,OAAO;gBAC9B;YACF;QACF;QACAnI,SAASmH,gBAAgB,GAAGzG,OAAOuE,IAAI,CAACjF,SAASgG,UAAU;QAE3D,OAAOhG;IACT;IAEA,MAAcoI,0BAAyC;QACrD,MAAMvC,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAACrG,mBAAmB,CAACwB,MAAM;QAGjC,8CAA8C;QAC9C,IAAK,MAAM9B,OAAO0G,mBAAmBG,UAAU,CAAE;YAC/CH,mBAAmBG,UAAU,CAAC7G,IAAI,CAAC4G,QAAQ,CAACsC,OAAO,CAAC,CAACT;gBACnD,IAAI,CAACA,QAAQC,MAAM,CAACS,UAAU,CAAC,MAAM;oBACnC,MAAMC,aAAavK,eAAe4J,QAAQC,MAAM;oBAChD,IAAIU,WAAWnD,KAAK,IAAI,CAACmD,WAAWC,QAAQ,EAAE;wBAC5C,MAAM,qBAA8C,CAA9C,IAAIC,MAAM,AAAC,qBAAkBb,QAAQC,MAAM,GAA3C,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6C;oBACrD;oBACAD,QAAQC,MAAM,GAAGU,WAAWC,QAAQ;gBACtC;YACF;QACF;QAEA,MAAMzB,yBAAyBhK,KAC7B,IAAI,CAACoB,OAAO,EACZ,UACA1B;QAEFS,YAAY6J;QACZ,MAAM5J,gBACJ4J,wBACAhI,KAAKsC,SAAS,CAACwE,oBAAoB,MAAM;IAE7C;IAEA,MAAM6C,kBAAkB5J,QAAgB,EAAiB;QACvD,IAAI,CAACY,cAAc,CAACG,GAAG,CACrBnC,YAAY,SAAS,UAAUoB,WAC/B,MAAMD,oBAAoB,IAAI,CAACV,OAAO,EAAExB,gBAAgBmC;IAE5D;IAEQiD,oBAAoBhC,SAAkC,EAAE;QAC9D,MAAMC,WAA0B,CAAC;QACjC,KAAK,MAAMY,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,UAAUY;QAC1B;QACA,OAAOE,gBAAgBd;IACzB;IAEA,MAAc2I,qBAAoC;QAChD,MAAMC,gBAAgB,IAAI,CAAC7G,mBAAmB,CAAC,IAAI,CAACrC,cAAc,CAACuB,MAAM;QACzE,MAAM4H,oBAAoB9L,KAAK,IAAI,CAACoB,OAAO,EAAE,UAAUxB;QACvDO,YAAY2L;QACZ,MAAM1L,gBACJ0L,mBACA9J,KAAKsC,SAAS,CAACuH,eAAe,MAAM;IAExC;IAEA,MAAME,eAAe,KAQpB,EAAE;QARkB,IAAA,EACnB3E,WAAW,EACXC,kBAAkB,EAClB7B,WAAW,EAKZ,GARoB;QASnB,MAAM,IAAI,CAACxB,mBAAmB;QAC9B,MAAM,IAAI,CAACU,qBAAqB;QAChC,MAAM,IAAI,CAACI,qBAAqB;QAChC,MAAM,IAAI,CAACqC,kBAAkB,CAAC3B,aAAa4B,aAAaC;QACxD,MAAM,IAAI,CAAC8B,0BAA0B;QACrC,MAAM,IAAI,CAACkC,uBAAuB;QAClC,MAAM,IAAI,CAACxC,6BAA6B;QACxC,MAAM,IAAI,CAACc,qBAAqB;QAChC,MAAM,IAAI,CAACiC,kBAAkB;QAE7B,IAAII,QAAQC,GAAG,CAACC,eAAe,IAAI,MAAM;YACvC,MAAM,IAAI,CAAChH,iBAAiB;QAC9B;IACF;IApnBAiH,YAAY,EACV/K,OAAO,EACP2F,OAAO,EACP3D,aAAa,EAKd,CAAE;aAtBKf,kBAAiD,IAAIqD;aACrDpD,oBAAqD,IAAIoD;aACzDnD,oBAAkD,IAAImD;aACtDlD,iBAA+C,IAAIkD;aACnDjD,gBAAiD,IAAIiD;aACrDhD,sBACN,IAAIgD;aACE/C,iBAA6C,IAAI+C;aACjD9C,eAA4C,IAAI8C;QAetD,IAAI,CAACtE,OAAO,GAAGA;QACf,IAAI,CAAC2F,OAAO,GAAGA;QACf,IAAI,CAAC3D,aAAa,GAAGA;IACvB;AAymBF;AAEA,SAASW,gBAAgBqI,GAAwB;IAC/C,OAAOzI,OAAOuE,IAAI,CAACkE,KAChBC,IAAI,GACJC,MAAM,CACL,CAACC,KAAKnK;QACJmK,GAAG,CAACnK,IAAI,GAAGgK,GAAG,CAAChK,IAAI;QACnB,OAAOmK;IACT,GACA,CAAC;AAEP"}