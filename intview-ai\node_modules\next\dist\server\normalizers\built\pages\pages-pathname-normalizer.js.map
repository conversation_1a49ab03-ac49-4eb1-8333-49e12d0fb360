{"version": 3, "sources": ["../../../../../src/server/normalizers/built/pages/pages-pathname-normalizer.ts"], "sourcesContent": ["import { PAGE_TYPES } from '../../../../lib/page-types'\nimport { AbsoluteFilenameNormalizer } from '../../absolute-filename-normalizer'\n\nexport class DevPagesPathnameNormalizer extends AbsoluteFilenameNormalizer {\n  constructor(pagesDir: string, extensions: ReadonlyArray<string>) {\n    super(pagesDir, extensions, PAGE_TYPES.PAGES)\n  }\n}\n"], "names": ["DevPagesPathnameNormalizer", "AbsoluteFilenameNormalizer", "constructor", "pagesDir", "extensions", "PAGE_TYPES", "PAGES"], "mappings": ";;;;+BAGaA;;;eAAAA;;;2BAHc;4CACgB;AAEpC,MAAMA,mCAAmCC,sDAA0B;IACxEC,YAAYC,QAAgB,EAAEC,UAAiC,CAAE;QAC/D,KAAK,CAACD,UAAUC,YAAYC,qBAAU,CAACC,KAAK;IAC9C;AACF"}