{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/index.ts"], "sourcesContent": ["import {\n  AppBundlePathNormalizer,\n  DevAppBundlePathNormalizer,\n} from './app-bundle-path-normalizer'\nimport { AppFilenameNormalizer } from './app-filename-normalizer'\nimport { DevAppPageNormalizer } from './app-page-normalizer'\nimport {\n  App<PERSON><PERSON><PERSON><PERSON><PERSON>ali<PERSON>,\n  DevAppPathnameNormalizer,\n} from './app-pathname-normalizer'\n\nexport class AppNormalizers {\n  public readonly filename: AppFilenameNormalizer\n  public readonly pathname: AppPathnameNormalizer\n  public readonly bundlePath: AppBundlePathNormalizer\n\n  constructor(distDir: string) {\n    this.filename = new AppFilenameNormalizer(distDir)\n    this.pathname = new AppPathnameNormalizer()\n    this.bundlePath = new AppBundlePathNormalizer()\n  }\n}\n\nexport class DevAppNormalizers {\n  public readonly page: DevAppPageNormalizer\n  public readonly pathname: DevAppPathnameNormalizer\n  public readonly bundlePath: DevAppBundlePathNormalizer\n\n  constructor(appDir: string, extensions: ReadonlyArray<string>) {\n    this.page = new DevAppPageNormalizer(appDir, extensions)\n    this.pathname = new DevAppPathnameNormalizer(this.page)\n    this.bundlePath = new DevAppBundlePathNormalizer(this.page)\n  }\n}\n"], "names": ["AppBundlePathNormalizer", "DevAppBundlePathNormalizer", "AppFilenameNormalizer", "DevAppPageNormalizer", "AppPathnameNormalizer", "DevAppPathnameNormalizer", "AppNormalizers", "constructor", "distDir", "filename", "pathname", "bundlePath", "DevAppNormalizers", "appDir", "extensions", "page"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,0BAA0B,QACrB,+BAA8B;AACrC,SAASC,qBAAqB,QAAQ,4BAA2B;AACjE,SAASC,oBAAoB,QAAQ,wBAAuB;AAC5D,SACEC,qBAAqB,EACrBC,wBAAwB,QACnB,4BAA2B;AAElC,OAAO,MAAMC;IAKXC,YAAYC,OAAe,CAAE;QAC3B,IAAI,CAACC,QAAQ,GAAG,IAAIP,sBAAsBM;QAC1C,IAAI,CAACE,QAAQ,GAAG,IAAIN;QACpB,IAAI,CAACO,UAAU,GAAG,IAAIX;IACxB;AACF;AAEA,OAAO,MAAMY;IAKXL,YAAYM,MAAc,EAAEC,UAAiC,CAAE;QAC7D,IAAI,CAACC,IAAI,GAAG,IAAIZ,qBAAqBU,QAAQC;QAC7C,IAAI,CAACJ,QAAQ,GAAG,IAAIL,yBAAyB,IAAI,CAACU,IAAI;QACtD,IAAI,CAACJ,UAAU,GAAG,IAAIV,2BAA2B,IAAI,CAACc,IAAI;IAC5D;AACF"}