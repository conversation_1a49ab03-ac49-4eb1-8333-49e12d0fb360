{"version": 3, "sources": ["../../src/server/render.tsx"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { NextRouter } from '../shared/lib/router/router'\nimport type { HtmlProps } from '../shared/lib/html-context.shared-runtime'\nimport type { DomainLocale } from './config'\nimport type {\n  AppType,\n  DocumentInitialProps,\n  DocumentType,\n  DocumentProps,\n  DocumentContext,\n  NextComponentType,\n  RenderPage,\n  RenderPageResult,\n} from '../shared/lib/utils'\nimport type { ImageConfigComplete } from '../shared/lib/image-config'\nimport type { Redirect } from '../lib/load-custom-routes'\nimport {\n  type NextApiRequestCookies,\n  type __ApiPreviewProps,\n  setLazyProp,\n} from './api-utils'\nimport { getCookieParser } from './api-utils/get-cookie-parser'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type {\n  GetServerSideProps,\n  GetStaticProps,\n  PreviewData,\n  ServerRuntime,\n  SizeLimit,\n} from '../types'\nimport type { UnwrapPromise } from '../lib/coalesced-function'\nimport type { ReactReadableStream } from './stream-utils/node-web-streams-helper'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type { PagesModule } from './route-modules/pages/module'\nimport type { ComponentsEnhancer } from '../shared/lib/utils'\nimport type { NextParsedUrlQuery } from './request-meta'\nimport type { Revalidate } from './lib/cache-control'\nimport type { COMPILER_NAMES } from '../shared/lib/constants'\n\nimport React, { type JSX } from 'react'\nimport ReactDOMServerPages from 'next/dist/server/ReactDOMServerPages'\nimport { StyleRegistry, createStyleRegistry } from 'styled-jsx'\nimport {\n  GSP_NO_RETURNED_VALUE,\n  GSSP_COMPONENT_MEMBER_ERROR,\n  GSSP_NO_RETURNED_VALUE,\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  UNSTABLE_REVALIDATE_RENAME_ERROR,\n} from '../lib/constants'\nimport {\n  NEXT_BUILTIN_DOCUMENT,\n  SERVER_PROPS_ID,\n  STATIC_PROPS_ID,\n  STATIC_STATUS_PAGES,\n} from '../shared/lib/constants'\nimport { isSerializableProps } from '../lib/is-serializable-props'\nimport { isInAmpMode } from '../shared/lib/amp-mode'\nimport { AmpStateContext } from '../shared/lib/amp-context.shared-runtime'\nimport { defaultHead } from '../shared/lib/head'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport Loadable from '../shared/lib/loadable.shared-runtime'\nimport { LoadableContext } from '../shared/lib/loadable-context.shared-runtime'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport {\n  getDisplayName,\n  isResSent,\n  loadGetInitialProps,\n} from '../shared/lib/utils'\nimport { HtmlContext } from '../shared/lib/html-context.shared-runtime'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { getRequestMeta } from './request-meta'\nimport { allowedStatusCodes, getRedirectStatus } from '../lib/redirect-status'\nimport RenderResult, { type PagesRenderResultMetadata } from './render-result'\nimport isError from '../lib/is-error'\nimport {\n  streamToString,\n  renderToInitialFizzStream,\n} from './stream-utils/node-web-streams-helper'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { stripInternalQueries } from './internal-utils'\nimport {\n  adaptForAppRouterInstance,\n  adaptForPathParams,\n  adaptForSearchParams,\n  PathnameContextProviderAdapter,\n} from '../shared/lib/router/adapters'\nimport { AppRouterContext } from '../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathParamsContext,\n} from '../shared/lib/hooks-client-context.shared-runtime'\nimport { getTracer } from './lib/trace/tracer'\nimport { RenderSpan } from './lib/trace/constants'\nimport { ReflectAdapter } from './web/spec-extension/adapters/reflect'\nimport { getCacheControlHeader } from './lib/cache-control'\nimport { getErrorSource } from '../shared/lib/error-source'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport type { PagesDevOverlayType } from '../client/components/react-dev-overlay/pages/pages-dev-overlay'\n\nlet tryGetPreviewData: typeof import('./api-utils/node/try-get-preview-data').tryGetPreviewData\nlet warn: typeof import('../build/output/log').warn\nlet postProcessHTML: typeof import('./post-process').postProcessHTML\n\nconst DOCTYPE = '<!DOCTYPE html>'\n\nif (process.env.NEXT_RUNTIME !== 'edge') {\n  tryGetPreviewData =\n    require('./api-utils/node/try-get-preview-data').tryGetPreviewData\n  warn = require('../build/output/log').warn\n  postProcessHTML = require('./post-process').postProcessHTML\n} else {\n  warn = console.warn.bind(console)\n  postProcessHTML = async (_pathname: string, html: string) => html\n}\n\nfunction noRouter() {\n  const message =\n    'No router instance found. you should only use \"next/router\" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance'\n  throw new Error(message)\n}\n\nasync function renderToString(element: React.ReactElement) {\n  const renderStream = await ReactDOMServerPages.renderToReadableStream(element)\n  await renderStream.allReady\n  return streamToString(renderStream)\n}\n\nclass ServerRouter implements NextRouter {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  events: any\n  isFallback: boolean\n  locale?: string\n  isReady: boolean\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  isPreview: boolean\n  isLocaleDomain: boolean\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    { isFallback }: { isFallback: boolean },\n    isReady: boolean,\n    basePath: string,\n    locale?: string,\n    locales?: readonly string[],\n    defaultLocale?: string,\n    domainLocales?: readonly DomainLocale[],\n    isPreview?: boolean,\n    isLocaleDomain?: boolean\n  ) {\n    this.route = pathname.replace(/\\/$/, '') || '/'\n    this.pathname = pathname\n    this.query = query\n    this.asPath = as\n    this.isFallback = isFallback\n    this.basePath = basePath\n    this.locale = locale\n    this.locales = locales\n    this.defaultLocale = defaultLocale\n    this.isReady = isReady\n    this.domainLocales = domainLocales\n    this.isPreview = !!isPreview\n    this.isLocaleDomain = !!isLocaleDomain\n  }\n\n  push(): any {\n    noRouter()\n  }\n  replace(): any {\n    noRouter()\n  }\n  reload() {\n    noRouter()\n  }\n  back() {\n    noRouter()\n  }\n  forward(): void {\n    noRouter()\n  }\n  prefetch(): any {\n    noRouter()\n  }\n  beforePopState() {\n    noRouter()\n  }\n}\n\nfunction enhanceComponents(\n  options: ComponentsEnhancer,\n  App: AppType,\n  Component: NextComponentType\n): {\n  App: AppType\n  Component: NextComponentType\n} {\n  // For backwards compatibility\n  if (typeof options === 'function') {\n    return {\n      App,\n      Component: options(Component),\n    }\n  }\n\n  return {\n    App: options.enhanceApp ? options.enhanceApp(App) : App,\n    Component: options.enhanceComponent\n      ? options.enhanceComponent(Component)\n      : Component,\n  }\n}\n\nfunction renderPageTree(\n  App: AppType,\n  Component: NextComponentType,\n  props: any\n) {\n  return <App Component={Component} {...props} />\n}\n\nexport type RenderOptsPartial = {\n  canonicalBase: string\n  runtimeConfig?: { [key: string]: any }\n  assetPrefix?: string\n  err?: Error | null\n  nextExport?: boolean\n  dev?: boolean\n  ampPath?: string\n  ErrorDebug?: PagesDevOverlayType\n  ampValidator?: (html: string, pathname: string) => Promise<void>\n  ampSkipValidation?: boolean\n  ampOptimizerConfig?: { [key: string]: any }\n  isNextDataRequest?: boolean\n  params?: ParsedUrlQuery\n  previewProps: __ApiPreviewProps | undefined\n  basePath: string\n  unstable_runtimeJS?: false\n  unstable_JsPreload?: false\n  optimizeCss: any\n  nextConfigOutput?: 'standalone' | 'export'\n  nextScriptWorkers: any\n  assetQueryString?: string\n  resolvedUrl?: string\n  resolvedAsPath?: string\n  setIsrStatus?: (key: string, value: boolean | null) => void\n  clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  nextFontManifest?: DeepReadonly<NextFontManifest>\n  distDir?: string\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  disableOptimizedLoading?: boolean\n  supportsDynamicResponse: boolean\n  botType?: 'dom' | 'html' | undefined\n  serveStreamingMetadata?: boolean\n  runtime?: ServerRuntime\n  serverComponents?: boolean\n  serverActions?: {\n    bodySizeLimit?: SizeLimit\n    allowedOrigins?: string[]\n  }\n  crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined\n  images: ImageConfigComplete\n  largePageDataBytes?: number\n  isOnDemandRevalidate?: boolean\n  strictNextHead: boolean\n  isPossibleServerAction?: boolean\n  isExperimentalCompile?: boolean\n  isPrefetch?: boolean\n  expireTime?: number\n  experimental: {\n    clientTraceMetadata?: string[]\n  }\n}\n\nexport type RenderOpts = LoadComponentsReturnType<PagesModule> &\n  RenderOptsPartial\n\n/**\n * Shared context used for all page renders.\n */\nexport type PagesSharedContext = {\n  /**\n   * Used to facilitate caching of page bundles, we send it to the client so\n   * that pageloader knows where to load bundles.\n   */\n  buildId: string\n\n  /**\n   * The deployment ID if the user is deploying to a platform that provides one.\n   */\n  deploymentId: string | undefined\n\n  /**\n   * True if the user is using a custom server.\n   */\n  customServer: true | undefined\n}\n\n/**\n * The context for the given request.\n */\nexport type PagesRenderContext = {\n  /**\n   * Whether this should be rendered as a fallback page.\n   */\n  isFallback: boolean\n\n  /**\n   * Whether this is in draft mode.\n   */\n  isDraftMode: boolean | undefined\n\n  /**\n   * In development, the original source page that returned a 404.\n   */\n  developmentNotFoundSourcePage: string | undefined\n}\n\n/**\n * RenderOptsExtra is being used to split away functionality that's within the\n * renderOpts. Eventually we can have more explicit render options for each\n * route kind.\n */\nexport type RenderOptsExtra = {\n  App: AppType\n  Document: DocumentType\n}\n\nconst invalidKeysMsg = (\n  methodName: 'getServerSideProps' | 'getStaticProps',\n  invalidKeys: string[]\n) => {\n  const docsPathname = `invalid-${methodName.toLocaleLowerCase()}-value`\n\n  return (\n    `Additional keys were returned from \\`${methodName}\\`. Properties intended for your component must be nested under the \\`props\\` key, e.g.:` +\n    `\\n\\n\\treturn { props: { title: 'My Title', content: '...' } }` +\n    `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.` +\n    `\\nRead more: https://nextjs.org/docs/messages/${docsPathname}`\n  )\n}\n\nfunction checkRedirectValues(\n  redirect: Redirect,\n  req: IncomingMessage,\n  method: 'getStaticProps' | 'getServerSideProps'\n) {\n  const { destination, permanent, statusCode, basePath } = redirect\n  let errors: string[] = []\n\n  const hasStatusCode = typeof statusCode !== 'undefined'\n  const hasPermanent = typeof permanent !== 'undefined'\n\n  if (hasPermanent && hasStatusCode) {\n    errors.push(`\\`permanent\\` and \\`statusCode\\` can not both be provided`)\n  } else if (hasPermanent && typeof permanent !== 'boolean') {\n    errors.push(`\\`permanent\\` must be \\`true\\` or \\`false\\``)\n  } else if (hasStatusCode && !allowedStatusCodes.has(statusCode!)) {\n    errors.push(\n      `\\`statusCode\\` must undefined or one of ${[...allowedStatusCodes].join(\n        ', '\n      )}`\n    )\n  }\n  const destinationType = typeof destination\n\n  if (destinationType !== 'string') {\n    errors.push(\n      `\\`destination\\` should be string but received ${destinationType}`\n    )\n  }\n\n  const basePathType = typeof basePath\n\n  if (basePathType !== 'undefined' && basePathType !== 'boolean') {\n    errors.push(\n      `\\`basePath\\` should be undefined or a false, received ${basePathType}`\n    )\n  }\n\n  if (errors.length > 0) {\n    throw new Error(\n      `Invalid redirect object returned from ${method} for ${req.url}\\n` +\n        errors.join(' and ') +\n        '\\n' +\n        `See more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp`\n    )\n  }\n}\n\nexport function errorToJSON(err: Error) {\n  let source: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer =\n    'server'\n\n  if (process.env.NEXT_RUNTIME !== 'edge') {\n    source = getErrorSource(err) || 'server'\n  }\n\n  return {\n    name: err.name,\n    source,\n    message: stripAnsi(err.message),\n    stack: err.stack,\n    digest: (err as any).digest,\n  }\n}\n\nfunction serializeError(\n  dev: boolean | undefined,\n  err: Error\n): Error & {\n  statusCode?: number\n  source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n} {\n  if (dev) {\n    return errorToJSON(err)\n  }\n\n  return {\n    name: 'Internal Server Error.',\n    message: '500 - Internal Server Error.',\n    statusCode: 500,\n  }\n}\n\nexport async function renderToHTMLImpl(\n  req: IncomingMessage,\n  res: ServerResponse,\n  pathname: string,\n  query: NextParsedUrlQuery,\n  renderOpts: Omit<RenderOpts, keyof RenderOptsExtra>,\n  extra: RenderOptsExtra,\n  sharedContext: PagesSharedContext,\n  renderContext: PagesRenderContext\n): Promise<RenderResult> {\n  // Adds support for reading `cookies` in `getServerSideProps` when SSR.\n  setLazyProp({ req: req as any }, 'cookies', getCookieParser(req.headers))\n\n  const metadata: PagesRenderResultMetadata = {}\n\n  metadata.assetQueryString =\n    (renderOpts.dev && renderOpts.assetQueryString) || ''\n\n  if (renderOpts.dev && !metadata.assetQueryString) {\n    const userAgent = (req.headers['user-agent'] || '').toLowerCase()\n    if (userAgent.includes('safari') && !userAgent.includes('chrome')) {\n      // In dev we invalidate the cache by appending a timestamp to the resource URL.\n      // This is a workaround to fix https://github.com/vercel/next.js/issues/5860\n      // TODO: remove this workaround when https://bugs.webkit.org/show_bug.cgi?id=187726 is fixed.\n      // Note: The workaround breaks breakpoints on reload since the script url always changes,\n      // so we only apply it to Safari.\n      metadata.assetQueryString = `?ts=${Date.now()}`\n    }\n  }\n\n  // if deploymentId is provided we append it to all asset requests\n  if (sharedContext.deploymentId) {\n    metadata.assetQueryString += `${metadata.assetQueryString ? '&' : '?'}dpl=${\n      sharedContext.deploymentId\n    }`\n  }\n\n  // don't modify original query object\n  query = Object.assign({}, query)\n\n  const {\n    err,\n    dev = false,\n    ampPath = '',\n    pageConfig = {},\n    buildManifest,\n    reactLoadableManifest,\n    ErrorDebug,\n    getStaticProps,\n    getStaticPaths,\n    getServerSideProps,\n    isNextDataRequest,\n    params,\n    previewProps,\n    basePath,\n    images,\n    runtime: globalRuntime,\n    isExperimentalCompile,\n    expireTime,\n  } = renderOpts\n  const { App } = extra\n\n  const assetQueryString = metadata.assetQueryString\n\n  let Document = extra.Document\n\n  let Component: React.ComponentType<{}> | ((props: any) => JSX.Element) =\n    renderOpts.Component\n  const OriginComponent = Component\n\n  const isFallback = renderContext.isFallback ?? false\n  const notFoundSrcPage = renderContext.developmentNotFoundSourcePage\n\n  // next internal queries should be stripped out\n  stripInternalQueries(query)\n\n  const isSSG = !!getStaticProps\n  const isBuildTimeSSG = isSSG && renderOpts.nextExport\n  const defaultAppGetInitialProps =\n    App.getInitialProps === (App as any).origGetInitialProps\n\n  const hasPageGetInitialProps = !!(Component as any)?.getInitialProps\n  const hasPageScripts = (Component as any)?.unstable_scriptLoader\n\n  const pageIsDynamic = isDynamicRoute(pathname)\n\n  const defaultErrorGetInitialProps =\n    pathname === '/_error' &&\n    (Component as any).getInitialProps ===\n      (Component as any).origGetInitialProps\n\n  if (\n    renderOpts.nextExport &&\n    hasPageGetInitialProps &&\n    !defaultErrorGetInitialProps\n  ) {\n    warn(\n      `Detected getInitialProps on page '${pathname}'` +\n        ` while running export. It's recommended to use getStaticProps` +\n        ` which has a more correct behavior for static exporting.` +\n        `\\nRead more: https://nextjs.org/docs/messages/get-initial-props-export`\n    )\n  }\n\n  let isAutoExport =\n    !hasPageGetInitialProps &&\n    defaultAppGetInitialProps &&\n    !isSSG &&\n    !getServerSideProps\n\n  // if we are running from experimental compile and the page\n  // would normally be automatically statically optimized\n  // ensure we set cache header so it's not rendered on-demand\n  // every request\n  if (isAutoExport && !dev && isExperimentalCompile) {\n    res.setHeader(\n      'Cache-Control',\n      getCacheControlHeader({ revalidate: false, expire: expireTime })\n    )\n    isAutoExport = false\n  }\n\n  if (hasPageGetInitialProps && isSSG) {\n    throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (hasPageGetInitialProps && getServerSideProps) {\n    throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getServerSideProps && isSSG) {\n    throw new Error(SERVER_PROPS_SSG_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getServerSideProps && renderOpts.nextConfigOutput === 'export') {\n    throw new Error(\n      'getServerSideProps cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n    )\n  }\n\n  if (getStaticPaths && !pageIsDynamic) {\n    throw new Error(\n      `getStaticPaths is only allowed for dynamic SSG pages and was found on '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`\n    )\n  }\n\n  if (!!getStaticPaths && !isSSG) {\n    throw new Error(\n      `getStaticPaths was added without a getStaticProps in ${pathname}. Without getStaticProps, getStaticPaths does nothing`\n    )\n  }\n\n  if (isSSG && pageIsDynamic && !getStaticPaths) {\n    throw new Error(\n      `getStaticPaths is required for dynamic SSG pages and is missing for '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n    )\n  }\n\n  let asPath: string = renderOpts.resolvedAsPath || (req.url as string)\n\n  if (dev) {\n    const { isValidElementType } = require('next/dist/compiled/react-is')\n    if (!isValidElementType(Component)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"${pathname}\"`\n      )\n    }\n\n    if (!isValidElementType(App)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_app\"`\n      )\n    }\n\n    if (!isValidElementType(Document)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_document\"`\n      )\n    }\n\n    if (isAutoExport || isFallback) {\n      // remove query values except ones that will be set during export\n      query = {\n        ...(query.amp\n          ? {\n              amp: query.amp,\n            }\n          : {}),\n      }\n      asPath = `${pathname}${\n        // ensure trailing slash is present for non-dynamic auto-export pages\n        req.url!.endsWith('/') && pathname !== '/' && !pageIsDynamic ? '/' : ''\n      }`\n      req.url = pathname\n    }\n\n    if (pathname === '/404' && (hasPageGetInitialProps || getServerSideProps)) {\n      throw new Error(\n        `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n    if (\n      STATIC_STATUS_PAGES.includes(pathname) &&\n      (hasPageGetInitialProps || getServerSideProps)\n    ) {\n      throw new Error(\n        `\\`pages${pathname}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n\n    if (renderOpts?.setIsrStatus) {\n      renderOpts.setIsrStatus(asPath, isSSG || isAutoExport ? true : null)\n    }\n  }\n\n  for (const methodName of [\n    'getStaticProps',\n    'getServerSideProps',\n    'getStaticPaths',\n  ]) {\n    if ((Component as any)?.[methodName]) {\n      throw new Error(\n        `page ${pathname} ${methodName} ${GSSP_COMPONENT_MEMBER_ERROR}`\n      )\n    }\n  }\n\n  await Loadable.preloadAll() // Make sure all dynamic imports are loaded\n\n  let isPreview: boolean | undefined = undefined\n  let previewData: PreviewData\n\n  if (\n    (isSSG || getServerSideProps) &&\n    !isFallback &&\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    previewProps\n  ) {\n    // Reads of this are cached on the `req` object, so this should resolve\n    // instantly. There's no need to pass this data down from a previous\n    // invoke.\n    previewData = tryGetPreviewData(\n      req,\n      res,\n      previewProps,\n      !!renderOpts.multiZoneDraftMode\n    )\n    isPreview = previewData !== false\n  }\n\n  // url will always be set\n  const routerIsReady = !!(\n    getServerSideProps ||\n    hasPageGetInitialProps ||\n    (!defaultAppGetInitialProps && !isSSG) ||\n    isExperimentalCompile\n  )\n  const router = new ServerRouter(\n    pathname,\n    query,\n    asPath,\n    {\n      isFallback: isFallback,\n    },\n    routerIsReady,\n    basePath,\n    renderOpts.locale,\n    renderOpts.locales,\n    renderOpts.defaultLocale,\n    renderOpts.domainLocales,\n    isPreview,\n    getRequestMeta(req, 'isLocaleDomain')\n  )\n\n  const appRouter = adaptForAppRouterInstance(router)\n\n  let scriptLoader: any = {}\n  const jsxStyleRegistry = createStyleRegistry()\n  const ampState = {\n    ampFirst: pageConfig.amp === true,\n    hasQuery: Boolean(query.amp),\n    hybrid: pageConfig.amp === 'hybrid',\n  }\n\n  // Disable AMP under the web environment\n  const inAmpMode = process.env.NEXT_RUNTIME !== 'edge' && isInAmpMode(ampState)\n  let head: JSX.Element[] = defaultHead(inAmpMode)\n  const reactLoadableModules: string[] = []\n\n  let initialScripts: any = {}\n  if (hasPageScripts) {\n    initialScripts.beforeInteractive = []\n      .concat(hasPageScripts())\n      .filter((script: any) => script.props.strategy === 'beforeInteractive')\n      .map((script: any) => script.props)\n  }\n\n  const AppContainer = ({ children }: { children: JSX.Element }) => (\n    <AppRouterContext.Provider value={appRouter}>\n      <SearchParamsContext.Provider value={adaptForSearchParams(router)}>\n        <PathnameContextProviderAdapter\n          router={router}\n          isAutoExport={isAutoExport}\n        >\n          <PathParamsContext.Provider value={adaptForPathParams(router)}>\n            <RouterContext.Provider value={router}>\n              <AmpStateContext.Provider value={ampState}>\n                <HeadManagerContext.Provider\n                  value={{\n                    updateHead: (state) => {\n                      head = state\n                    },\n                    updateScripts: (scripts) => {\n                      scriptLoader = scripts\n                    },\n                    scripts: initialScripts,\n                    mountedInstances: new Set(),\n                  }}\n                >\n                  <LoadableContext.Provider\n                    value={(moduleName) =>\n                      reactLoadableModules.push(moduleName)\n                    }\n                  >\n                    <StyleRegistry registry={jsxStyleRegistry}>\n                      <ImageConfigContext.Provider value={images}>\n                        {children}\n                      </ImageConfigContext.Provider>\n                    </StyleRegistry>\n                  </LoadableContext.Provider>\n                </HeadManagerContext.Provider>\n              </AmpStateContext.Provider>\n            </RouterContext.Provider>\n          </PathParamsContext.Provider>\n        </PathnameContextProviderAdapter>\n      </SearchParamsContext.Provider>\n    </AppRouterContext.Provider>\n  )\n\n  // The `useId` API uses the path indexes to generate an ID for each node.\n  // To guarantee the match of hydration, we need to ensure that the structure\n  // of wrapper nodes is isomorphic in server and client.\n  // TODO: With `enhanceApp` and `enhanceComponents` options, this approach may\n  // not be useful.\n  // https://github.com/facebook/react/pull/22644\n  const Noop = () => null\n  const AppContainerWithIsomorphicFiberStructure: React.FC<{\n    children: JSX.Element\n  }> = ({ children }) => {\n    return (\n      <>\n        {/* <Head/> */}\n        <Noop />\n        <AppContainer>\n          <>\n            {/* <ReactDevOverlay/> */}\n            {dev ? (\n              <>\n                {children}\n                <Noop />\n              </>\n            ) : (\n              children\n            )}\n            {/* <RouteAnnouncer/> */}\n            <Noop />\n          </>\n        </AppContainer>\n      </>\n    )\n  }\n\n  const ctx = {\n    err,\n    req: isAutoExport ? undefined : req,\n    res: isAutoExport ? undefined : res,\n    pathname,\n    query,\n    asPath,\n    locale: renderOpts.locale,\n    locales: renderOpts.locales,\n    defaultLocale: renderOpts.defaultLocale,\n    AppTree: (props: any) => {\n      return (\n        <AppContainerWithIsomorphicFiberStructure>\n          {renderPageTree(App, OriginComponent, { ...props, router })}\n        </AppContainerWithIsomorphicFiberStructure>\n      )\n    },\n    defaultGetInitialProps: async (\n      docCtx: DocumentContext,\n      options: { nonce?: string } = {}\n    ): Promise<DocumentInitialProps> => {\n      const enhanceApp = (AppComp: any) => {\n        return (props: any) => <AppComp {...props} />\n      }\n\n      const { html, head: renderPageHead } = await docCtx.renderPage({\n        enhanceApp,\n      })\n      const styles = jsxStyleRegistry.styles({ nonce: options.nonce })\n      jsxStyleRegistry.flush()\n      return { html, head: renderPageHead, styles }\n    },\n  }\n  let props: any\n\n  const nextExport =\n    !isSSG && (renderOpts.nextExport || (dev && (isAutoExport || isFallback)))\n\n  const styledJsxInsertedHTML = () => {\n    const styles = jsxStyleRegistry.styles()\n    jsxStyleRegistry.flush()\n    return <>{styles}</>\n  }\n\n  props = await loadGetInitialProps(App, {\n    AppTree: ctx.AppTree,\n    Component,\n    router,\n    ctx,\n  })\n\n  if ((isSSG || getServerSideProps) && isPreview) {\n    props.__N_PREVIEW = true\n  }\n\n  if (isSSG) {\n    props[STATIC_PROPS_ID] = true\n  }\n\n  if (isSSG && !isFallback) {\n    let data: Readonly<UnwrapPromise<ReturnType<GetStaticProps>>>\n\n    try {\n      data = await getTracer().trace(\n        RenderSpan.getStaticProps,\n        {\n          spanName: `getStaticProps ${pathname}`,\n          attributes: {\n            'next.route': pathname,\n          },\n        },\n        () =>\n          getStaticProps({\n            ...(pageIsDynamic ? { params } : undefined),\n            ...(isPreview\n              ? { draftMode: true, preview: true, previewData: previewData }\n              : undefined),\n            locales: [...(renderOpts.locales ?? [])],\n            locale: renderOpts.locale,\n            defaultLocale: renderOpts.defaultLocale,\n            revalidateReason: renderOpts.isOnDemandRevalidate\n              ? 'on-demand'\n              : isBuildTimeSSG\n                ? 'build'\n                : 'stale',\n          })\n      )\n    } catch (staticPropsError: any) {\n      // remove not found error code to prevent triggering legacy\n      // 404 rendering\n      if (staticPropsError && staticPropsError.code === 'ENOENT') {\n        delete staticPropsError.code\n      }\n      throw staticPropsError\n    }\n\n    if (data == null) {\n      throw new Error(GSP_NO_RETURNED_VALUE)\n    }\n\n    const invalidKeys = Object.keys(data).filter(\n      (key) =>\n        key !== 'revalidate' &&\n        key !== 'props' &&\n        key !== 'redirect' &&\n        key !== 'notFound'\n    )\n\n    if (invalidKeys.includes('unstable_revalidate')) {\n      throw new Error(UNSTABLE_REVALIDATE_RENAME_ERROR)\n    }\n\n    if (invalidKeys.length) {\n      throw new Error(invalidKeysMsg('getStaticProps', invalidKeys))\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (\n        typeof (data as any).notFound !== 'undefined' &&\n        typeof (data as any).redirect !== 'undefined'\n      ) {\n        throw new Error(\n          `\\`redirect\\` and \\`notFound\\` can not both be returned from ${\n            isSSG ? 'getStaticProps' : 'getServerSideProps'\n          } at the same time. Page: ${pathname}\\nSee more info here: https://nextjs.org/docs/messages/gssp-mixed-not-found-redirect`\n        )\n      }\n    }\n\n    if ('notFound' in data && data.notFound) {\n      if (pathname === '/404') {\n        throw new Error(\n          `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n        )\n      }\n\n      metadata.isNotFound = true\n    }\n\n    if (\n      'redirect' in data &&\n      data.redirect &&\n      typeof data.redirect === 'object'\n    ) {\n      checkRedirectValues(data.redirect as Redirect, req, 'getStaticProps')\n\n      if (isBuildTimeSSG) {\n        throw new Error(\n          `\\`redirect\\` can not be returned from getStaticProps during prerendering (${req.url})\\n` +\n            `See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`\n        )\n      }\n\n      ;(data as any).props = {\n        __N_REDIRECT: data.redirect.destination,\n        __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n      }\n      if (typeof data.redirect.basePath !== 'undefined') {\n        ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n      }\n      metadata.isRedirect = true\n    }\n\n    if (\n      (dev || isBuildTimeSSG) &&\n      !metadata.isNotFound &&\n      !isSerializableProps(pathname, 'getStaticProps', (data as any).props)\n    ) {\n      // this fn should throw an error instead of ever returning `false`\n      throw new Error(\n        'invariant: getStaticProps did not return valid props. Please report this.'\n      )\n    }\n\n    let revalidate: Revalidate\n    if ('revalidate' in data) {\n      if (data.revalidate && renderOpts.nextConfigOutput === 'export') {\n        throw new Error(\n          'ISR cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n        )\n      }\n      if (typeof data.revalidate === 'number') {\n        if (!Number.isInteger(data.revalidate)) {\n          throw new Error(\n            `A page's revalidate option must be seconds expressed as a natural number for ${req.url}. Mixed numbers, such as '${data.revalidate}', cannot be used.` +\n              `\\nTry changing the value to '${Math.ceil(\n                data.revalidate\n              )}' or using \\`Math.ceil()\\` if you're computing the value.`\n          )\n        } else if (data.revalidate <= 0) {\n          throw new Error(\n            `A page's revalidate option can not be less than or equal to zero for ${req.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.` +\n              `\\n\\nTo never revalidate, you can set revalidate to \\`false\\` (only ran once at build-time).` +\n              `\\nTo revalidate as soon as possible, you can set the value to \\`1\\`.`\n          )\n        } else {\n          if (data.revalidate > 31536000) {\n            // if it's greater than a year for some reason error\n            console.warn(\n              `Warning: A page's revalidate option was set to more than a year for ${req.url}. This may have been done in error.` +\n                `\\nTo only run getStaticProps at build-time and not revalidate at runtime, you can set \\`revalidate\\` to \\`false\\`!`\n            )\n          }\n\n          revalidate = data.revalidate\n        }\n      } else if (data.revalidate === true) {\n        // When enabled, revalidate after 1 second. This value is optimal for\n        // the most up-to-date page possible, but without a 1-to-1\n        // request-refresh ratio.\n        revalidate = 1\n      } else if (\n        data.revalidate === false ||\n        typeof data.revalidate === 'undefined'\n      ) {\n        // By default, we never revalidate.\n        revalidate = false\n      } else {\n        throw new Error(\n          `A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(\n            data.revalidate\n          )}' for ${req.url}`\n        )\n      }\n    } else {\n      // By default, we never revalidate.\n      revalidate = false\n    }\n\n    props.pageProps = Object.assign(\n      {},\n      props.pageProps,\n      'props' in data ? data.props : undefined\n    )\n\n    // pass up cache control and props for export\n    metadata.cacheControl = { revalidate, expire: undefined }\n    metadata.pageData = props\n\n    // this must come after revalidate is added to renderResultMeta\n    if (metadata.isNotFound) {\n      return new RenderResult(null, { metadata })\n    }\n  }\n\n  if (getServerSideProps) {\n    props[SERVER_PROPS_ID] = true\n  }\n\n  if (getServerSideProps && !isFallback) {\n    let data: UnwrapPromise<ReturnType<GetServerSideProps>>\n\n    let canAccessRes = true\n    let resOrProxy = res\n    let deferredContent = false\n    if (process.env.NODE_ENV !== 'production') {\n      resOrProxy = new Proxy<ServerResponse>(res, {\n        get: function (obj, prop) {\n          if (!canAccessRes) {\n            const message =\n              `You should not access 'res' after getServerSideProps resolves.` +\n              `\\nRead more: https://nextjs.org/docs/messages/gssp-no-mutating-res`\n\n            if (deferredContent) {\n              throw new Error(message)\n            } else {\n              warn(message)\n            }\n          }\n\n          if (typeof prop === 'symbol') {\n            return ReflectAdapter.get(obj, prop, res)\n          }\n\n          return ReflectAdapter.get(obj, prop, res)\n        },\n      })\n    }\n\n    try {\n      data = await getTracer().trace(\n        RenderSpan.getServerSideProps,\n        {\n          spanName: `getServerSideProps ${pathname}`,\n          attributes: {\n            'next.route': pathname,\n          },\n        },\n        async () =>\n          getServerSideProps({\n            req: req as IncomingMessage & {\n              cookies: NextApiRequestCookies\n            },\n            res: resOrProxy,\n            query,\n            resolvedUrl: renderOpts.resolvedUrl as string,\n            ...(pageIsDynamic ? { params } : undefined),\n            ...(previewData !== false\n              ? { draftMode: true, preview: true, previewData: previewData }\n              : undefined),\n            // We create a copy here to avoid having the types of\n            // `getServerSideProps` change. This ensures that users can't\n            // mutate this array and have it poison the reference.\n            locales: [...(renderOpts.locales ?? [])],\n            locale: renderOpts.locale,\n            defaultLocale: renderOpts.defaultLocale,\n          })\n      )\n      canAccessRes = false\n      metadata.cacheControl = { revalidate: 0, expire: undefined }\n    } catch (serverSidePropsError: any) {\n      // remove not found error code to prevent triggering legacy\n      // 404 rendering\n      if (\n        isError(serverSidePropsError) &&\n        serverSidePropsError.code === 'ENOENT'\n      ) {\n        delete serverSidePropsError.code\n      }\n      throw serverSidePropsError\n    }\n\n    if (data == null) {\n      throw new Error(GSSP_NO_RETURNED_VALUE)\n    }\n\n    if ((data as any).props instanceof Promise) {\n      deferredContent = true\n    }\n\n    const invalidKeys = Object.keys(data).filter(\n      (key) => key !== 'props' && key !== 'redirect' && key !== 'notFound'\n    )\n\n    if ((data as any).unstable_notFound) {\n      throw new Error(\n        `unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${pathname}`\n      )\n    }\n    if ((data as any).unstable_redirect) {\n      throw new Error(\n        `unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${pathname}`\n      )\n    }\n\n    if (invalidKeys.length) {\n      throw new Error(invalidKeysMsg('getServerSideProps', invalidKeys))\n    }\n\n    if ('notFound' in data && data.notFound) {\n      if (pathname === '/404') {\n        throw new Error(\n          `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n        )\n      }\n\n      metadata.isNotFound = true\n      return new RenderResult(null, { metadata })\n    }\n\n    if ('redirect' in data && typeof data.redirect === 'object') {\n      checkRedirectValues(data.redirect as Redirect, req, 'getServerSideProps')\n      ;(data as any).props = {\n        __N_REDIRECT: data.redirect.destination,\n        __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n      }\n      if (typeof data.redirect.basePath !== 'undefined') {\n        ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n      }\n      metadata.isRedirect = true\n    }\n\n    if (deferredContent) {\n      ;(data as any).props = await (data as any).props\n    }\n\n    if (\n      (dev || isBuildTimeSSG) &&\n      !isSerializableProps(pathname, 'getServerSideProps', (data as any).props)\n    ) {\n      // this fn should throw an error instead of ever returning `false`\n      throw new Error(\n        'invariant: getServerSideProps did not return valid props. Please report this.'\n      )\n    }\n\n    props.pageProps = Object.assign({}, props.pageProps, (data as any).props)\n    metadata.pageData = props\n  }\n\n  if (\n    !isSSG && // we only show this warning for legacy pages\n    !getServerSideProps &&\n    process.env.NODE_ENV !== 'production' &&\n    Object.keys(props?.pageProps || {}).includes('url')\n  ) {\n    console.warn(\n      `The prop \\`url\\` is a reserved prop in Next.js for legacy reasons and will be overridden on page ${pathname}\\n` +\n        `See more info here: https://nextjs.org/docs/messages/reserved-page-prop`\n    )\n  }\n\n  // Avoid rendering page un-necessarily for getServerSideProps data request\n  // and getServerSideProps/getStaticProps redirects\n  if ((isNextDataRequest && !isSSG) || metadata.isRedirect) {\n    return new RenderResult(JSON.stringify(props), {\n      metadata,\n    })\n  }\n\n  // We don't call getStaticProps or getServerSideProps while generating\n  // the fallback so make sure to set pageProps to an empty object\n  if (isFallback) {\n    props.pageProps = {}\n  }\n\n  // the response might be finished on the getInitialProps call\n  if (isResSent(res) && !isSSG) return new RenderResult(null, { metadata })\n\n  // we preload the buildManifest for auto-export dynamic pages\n  // to speed up hydrating query values\n  let filteredBuildManifest = buildManifest\n  if (isAutoExport && pageIsDynamic) {\n    const page = denormalizePagePath(normalizePagePath(pathname))\n    // This code would be much cleaner using `immer` and directly pushing into\n    // the result from `getPageFiles`, we could maybe consider that in the\n    // future.\n    if (page in filteredBuildManifest.pages) {\n      filteredBuildManifest = {\n        ...filteredBuildManifest,\n        pages: {\n          ...filteredBuildManifest.pages,\n          [page]: [\n            ...filteredBuildManifest.pages[page],\n            ...filteredBuildManifest.lowPriorityFiles.filter((f) =>\n              f.includes('_buildManifest')\n            ),\n          ],\n        },\n        lowPriorityFiles: filteredBuildManifest.lowPriorityFiles.filter(\n          (f) => !f.includes('_buildManifest')\n        ),\n      }\n    }\n  }\n\n  const Body = ({ children }: { children: JSX.Element }) => {\n    return inAmpMode ? children : <div id=\"__next\">{children}</div>\n  }\n\n  const renderDocument = async () => {\n    // For `Document`, there are two cases that we don't support:\n    // 1. Using `Document.getInitialProps` in the Edge runtime.\n    // 2. Using the class component `Document` with concurrent features.\n\n    const BuiltinFunctionalDocument: DocumentType | undefined = (\n      Document as any\n    )[NEXT_BUILTIN_DOCUMENT]\n\n    if (process.env.NEXT_RUNTIME === 'edge' && Document.getInitialProps) {\n      // In the Edge runtime, `Document.getInitialProps` isn't supported.\n      // We throw an error here if it's customized.\n      if (BuiltinFunctionalDocument) {\n        Document = BuiltinFunctionalDocument\n      } else {\n        throw new Error(\n          '`getInitialProps` in Document component is not supported with the Edge Runtime.'\n        )\n      }\n    }\n\n    async function loadDocumentInitialProps(\n      renderShell: (\n        _App: AppType,\n        _Component: NextComponentType\n      ) => Promise<ReactReadableStream>\n    ) {\n      const renderPage: RenderPage = async (\n        options: ComponentsEnhancer = {}\n      ): Promise<RenderPageResult> => {\n        if (ctx.err && ErrorDebug) {\n          // Always start rendering the shell even if there's an error.\n          if (renderShell) {\n            renderShell(App, Component)\n          }\n\n          const html = await renderToString(\n            <Body>\n              <ErrorDebug />\n            </Body>\n          )\n          return { html, head }\n        }\n\n        if (dev && (props.router || props.Component)) {\n          throw new Error(\n            `'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props`\n          )\n        }\n\n        const { App: EnhancedApp, Component: EnhancedComponent } =\n          enhanceComponents(options, App, Component)\n\n        const stream = await renderShell(EnhancedApp, EnhancedComponent)\n        await stream.allReady\n        const html = await streamToString(stream)\n\n        return { html, head }\n      }\n      const documentCtx = { ...ctx, renderPage }\n      const docProps: DocumentInitialProps = await loadGetInitialProps(\n        Document,\n        documentCtx\n      )\n      // the response might be finished on the getInitialProps call\n      if (isResSent(res) && !isSSG) return null\n\n      if (!docProps || typeof docProps.html !== 'string') {\n        const message = `\"${getDisplayName(\n          Document\n        )}.getInitialProps()\" should resolve to an object with a \"html\" prop set with a valid html string`\n        throw new Error(message)\n      }\n\n      return { docProps, documentCtx }\n    }\n\n    const renderContent = (_App: AppType, _Component: NextComponentType) => {\n      const EnhancedApp = _App || App\n      const EnhancedComponent = _Component || Component\n\n      return ctx.err && ErrorDebug ? (\n        <Body>\n          <ErrorDebug />\n        </Body>\n      ) : (\n        <Body>\n          <AppContainerWithIsomorphicFiberStructure>\n            {renderPageTree(EnhancedApp, EnhancedComponent, {\n              ...props,\n              router,\n            })}\n          </AppContainerWithIsomorphicFiberStructure>\n        </Body>\n      )\n    }\n\n    // Always using react concurrent rendering mode with required react version 18.x\n    const renderShell = async (\n      EnhancedApp: AppType,\n      EnhancedComponent: NextComponentType\n    ) => {\n      const content = renderContent(EnhancedApp, EnhancedComponent)\n      return await renderToInitialFizzStream({\n        ReactDOMServer: ReactDOMServerPages,\n        element: content,\n      })\n    }\n\n    const hasDocumentGetInitialProps =\n      process.env.NEXT_RUNTIME !== 'edge' && !!Document.getInitialProps\n\n    // If it has getInitialProps, we will render the shell in `renderPage`.\n    // Otherwise we do it right now.\n    let documentInitialPropsRes:\n      | {}\n      | Awaited<ReturnType<typeof loadDocumentInitialProps>>\n\n    const [rawStyledJsxInsertedHTML, content] = await Promise.all([\n      renderToString(styledJsxInsertedHTML()),\n      (async () => {\n        if (hasDocumentGetInitialProps) {\n          documentInitialPropsRes = await loadDocumentInitialProps(renderShell)\n          if (documentInitialPropsRes === null) return null\n          const { docProps } = documentInitialPropsRes as any\n          return docProps.html\n        } else {\n          documentInitialPropsRes = {}\n          const stream = await renderShell(App, Component)\n          await stream.allReady\n          return streamToString(stream)\n        }\n      })(),\n    ])\n\n    if (content === null) {\n      return null\n    }\n\n    const contentHTML = rawStyledJsxInsertedHTML + content\n\n    // @ts-ignore: documentInitialPropsRes is set\n    const { docProps } = (documentInitialPropsRes as any) || {}\n    const documentElement = (htmlProps: any) => {\n      if (process.env.NEXT_RUNTIME === 'edge') {\n        return (Document as any)()\n      } else {\n        return <Document {...htmlProps} {...docProps} />\n      }\n    }\n\n    let styles\n    if (hasDocumentGetInitialProps) {\n      styles = docProps.styles\n      head = docProps.head\n    } else {\n      styles = jsxStyleRegistry.styles()\n      jsxStyleRegistry.flush()\n    }\n\n    return {\n      contentHTML,\n      documentElement,\n      head,\n      headTags: [],\n      styles,\n    }\n  }\n\n  getTracer().setRootSpanAttribute('next.route', renderOpts.page)\n  const documentResult = await getTracer().trace(\n    RenderSpan.renderDocument,\n    {\n      spanName: `render route (pages) ${renderOpts.page}`,\n      attributes: {\n        'next.route': renderOpts.page,\n      },\n    },\n    async () => renderDocument()\n  )\n  if (!documentResult) {\n    return new RenderResult(null, { metadata })\n  }\n\n  const dynamicImportsIds = new Set<string | number>()\n  const dynamicImports = new Set<string>()\n\n  for (const mod of reactLoadableModules) {\n    const manifestItem = reactLoadableManifest[mod]\n\n    if (manifestItem) {\n      dynamicImportsIds.add(manifestItem.id)\n      manifestItem.files.forEach((item) => {\n        dynamicImports.add(item)\n      })\n    }\n  }\n\n  const hybridAmp = ampState.hybrid\n  const docComponentsRendered: DocumentProps['docComponentsRendered'] = {}\n\n  const {\n    assetPrefix,\n    defaultLocale,\n    disableOptimizedLoading,\n    domainLocales,\n    locale,\n    locales,\n    runtimeConfig,\n  } = renderOpts\n  const htmlProps: HtmlProps = {\n    __NEXT_DATA__: {\n      props, // The result of getInitialProps\n      page: pathname, // The rendered page\n      query, // querystring parsed / passed by the user\n      buildId: sharedContext.buildId,\n      assetPrefix: assetPrefix === '' ? undefined : assetPrefix, // send assetPrefix to the client side when configured, otherwise don't sent in the resulting HTML\n      runtimeConfig, // runtimeConfig if provided, otherwise don't sent in the resulting HTML\n      nextExport: nextExport === true ? true : undefined, // If this is a page exported by `next export`\n      autoExport: isAutoExport === true ? true : undefined, // If this is an auto exported page\n      isFallback,\n      isExperimentalCompile,\n      dynamicIds:\n        dynamicImportsIds.size === 0\n          ? undefined\n          : Array.from(dynamicImportsIds),\n      err: renderOpts.err ? serializeError(dev, renderOpts.err) : undefined, // Error if one happened, otherwise don't sent in the resulting HTML\n      gsp: !!getStaticProps ? true : undefined, // whether the page is getStaticProps\n      gssp: !!getServerSideProps ? true : undefined, // whether the page is getServerSideProps\n      customServer: sharedContext.customServer,\n      gip: hasPageGetInitialProps ? true : undefined, // whether the page has getInitialProps\n      appGip: !defaultAppGetInitialProps ? true : undefined, // whether the _app has getInitialProps\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview: isPreview === true ? true : undefined,\n      notFoundSrcPage: notFoundSrcPage && dev ? notFoundSrcPage : undefined,\n    },\n    strictNextHead: renderOpts.strictNextHead,\n    buildManifest: filteredBuildManifest,\n    docComponentsRendered,\n    dangerousAsPath: router.asPath,\n    canonicalBase:\n      !renderOpts.ampPath && getRequestMeta(req, 'didStripLocale')\n        ? `${renderOpts.canonicalBase || ''}/${renderOpts.locale}`\n        : renderOpts.canonicalBase,\n    ampPath,\n    inAmpMode,\n    isDevelopment: !!dev,\n    hybridAmp,\n    dynamicImports: Array.from(dynamicImports),\n    dynamicCssManifest: new Set(renderOpts.dynamicCssManifest || []),\n    assetPrefix,\n    // Only enabled in production as development mode has features relying on HMR (style injection for example)\n    unstable_runtimeJS:\n      process.env.NODE_ENV === 'production'\n        ? pageConfig.unstable_runtimeJS\n        : undefined,\n    unstable_JsPreload: pageConfig.unstable_JsPreload,\n    assetQueryString,\n    scriptLoader,\n    locale,\n    disableOptimizedLoading,\n    head: documentResult.head,\n    headTags: documentResult.headTags,\n    styles: documentResult.styles,\n    crossOrigin: renderOpts.crossOrigin,\n    optimizeCss: renderOpts.optimizeCss,\n    nextConfigOutput: renderOpts.nextConfigOutput,\n    nextScriptWorkers: renderOpts.nextScriptWorkers,\n    runtime: globalRuntime,\n    largePageDataBytes: renderOpts.largePageDataBytes,\n    nextFontManifest: renderOpts.nextFontManifest,\n    experimentalClientTraceMetadata:\n      renderOpts.experimental.clientTraceMetadata,\n  }\n\n  const document = (\n    <AmpStateContext.Provider value={ampState}>\n      <HtmlContext.Provider value={htmlProps}>\n        {documentResult.documentElement(htmlProps)}\n      </HtmlContext.Provider>\n    </AmpStateContext.Provider>\n  )\n\n  const documentHTML = await getTracer().trace(\n    RenderSpan.renderToString,\n    async () => renderToString(document)\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    const nonRenderedComponents = []\n    const expectedDocComponents = ['Main', 'Head', 'NextScript', 'Html']\n\n    for (const comp of expectedDocComponents) {\n      if (!(docComponentsRendered as any)[comp]) {\n        nonRenderedComponents.push(comp)\n      }\n    }\n\n    if (nonRenderedComponents.length) {\n      const missingComponentList = nonRenderedComponents\n        .map((e) => `<${e} />`)\n        .join(', ')\n      const plural = nonRenderedComponents.length !== 1 ? 's' : ''\n      console.warn(\n        `Your custom Document (pages/_document) did not render all the required subcomponent${plural}.\\n` +\n          `Missing component${plural}: ${missingComponentList}\\n` +\n          'Read how to fix here: https://nextjs.org/docs/messages/missing-document-component'\n      )\n    }\n  }\n\n  const [renderTargetPrefix, renderTargetSuffix] = documentHTML.split(\n    '<next-js-internal-body-render-target></next-js-internal-body-render-target>',\n    2\n  )\n\n  let prefix = ''\n  if (!documentHTML.startsWith(DOCTYPE)) {\n    prefix += DOCTYPE\n  }\n  prefix += renderTargetPrefix\n  if (inAmpMode) {\n    prefix += '<!-- __NEXT_DATA__ -->'\n  }\n\n  const content = prefix + documentResult.contentHTML + renderTargetSuffix\n\n  const optimizedHtml = await postProcessHTML(pathname, content, renderOpts, {\n    inAmpMode,\n    hybridAmp,\n  })\n\n  return new RenderResult(optimizedHtml, { metadata })\n}\n\nexport type PagesRender = (\n  req: IncomingMessage,\n  res: ServerResponse,\n  pathname: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  sharedContext: PagesSharedContext,\n  renderContext: PagesRenderContext\n) => Promise<RenderResult>\n\nexport const renderToHTML: PagesRender = (\n  req,\n  res,\n  pathname,\n  query,\n  renderOpts,\n  sharedContext,\n  renderContext\n) => {\n  return renderToHTMLImpl(\n    req,\n    res,\n    pathname,\n    query,\n    renderOpts,\n    renderOpts,\n    sharedContext,\n    renderContext\n  )\n}\n"], "names": ["errorToJSON", "renderToHTML", "renderToHTMLImpl", "tryGetPreviewData", "warn", "postProcessHTML", "DOCTYPE", "process", "env", "NEXT_RUNTIME", "require", "console", "bind", "_pathname", "html", "noRouter", "message", "Error", "renderToString", "element", "renderStream", "ReactDOMServerPages", "renderToReadableStream", "allReady", "streamToString", "ServerRouter", "constructor", "pathname", "query", "as", "<PERSON><PERSON><PERSON><PERSON>", "isReady", "basePath", "locale", "locales", "defaultLocale", "domainLocales", "isPreview", "isLocaleDomain", "route", "replace", "<PERSON><PERSON><PERSON>", "push", "reload", "back", "forward", "prefetch", "beforePopState", "enhanceComponents", "options", "App", "Component", "enhanceApp", "enhanceComponent", "renderPageTree", "props", "invalidKeysMsg", "methodName", "<PERSON><PERSON><PERSON><PERSON>", "docsPathname", "toLocaleLowerCase", "join", "checkRedirectValues", "redirect", "req", "method", "destination", "permanent", "statusCode", "errors", "hasStatusCode", "hasPermanent", "allowedStatusCodes", "has", "destinationType", "basePathType", "length", "url", "err", "source", "getErrorSource", "name", "stripAnsi", "stack", "digest", "serializeError", "dev", "res", "renderOpts", "extra", "sharedContext", "renderContext", "setLazyProp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "metadata", "assetQueryString", "userAgent", "toLowerCase", "includes", "Date", "now", "deploymentId", "Object", "assign", "ampPath", "pageConfig", "buildManifest", "reactLoadableManifest", "ErrorDebug", "getStaticProps", "getStaticPaths", "getServerSideProps", "isNextDataRequest", "params", "previewProps", "images", "runtime", "globalRuntime", "isExperimentalCompile", "expireTime", "Document", "OriginComponent", "notFoundSrcPage", "developmentNotFoundSourcePage", "stripInternalQueries", "isSSG", "isBuildTimeSSG", "nextExport", "defaultAppGetInitialProps", "getInitialProps", "origGetInitialProps", "hasPageGetInitialProps", "hasPageScripts", "unstable_scriptLoader", "pageIsDynamic", "isDynamicRoute", "defaultErrorGetInitialProps", "isAutoExport", "<PERSON><PERSON><PERSON><PERSON>", "getCacheControlHeader", "revalidate", "expire", "SSG_GET_INITIAL_PROPS_CONFLICT", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "nextConfigOutput", "resolvedAsPath", "isValidElementType", "amp", "endsWith", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "STATIC_STATUS_PAGES", "setIsrStatus", "GSSP_COMPONENT_MEMBER_ERROR", "Loadable", "preloadAll", "undefined", "previewData", "multiZoneDraftMode", "routerIsReady", "router", "getRequestMeta", "appRouter", "adaptForAppRouterInstance", "<PERSON><PERSON><PERSON><PERSON>", "jsxStyleRegistry", "createStyleRegistry", "ampState", "ampFirs<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "hybrid", "inAmpMode", "isInAmpMode", "head", "defaultHead", "reactLoadableModules", "initialScripts", "beforeInteractive", "concat", "filter", "script", "strategy", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "AppRouterContext", "Provider", "value", "SearchParamsContext", "adaptForSearchParams", "PathnameContextProviderAdapter", "PathParamsContext", "adaptForPathParams", "RouterContext", "AmpStateContext", "HeadManagerContext", "updateHead", "state", "updateScripts", "scripts", "mountedInstances", "Set", "LoadableContext", "moduleName", "StyleRegistry", "registry", "ImageConfigContext", "Noop", "AppContainerWithIsomorphicFiberStructure", "ctx", "AppTree", "defaultGetInitialProps", "docCtx", "AppComp", "renderPageHead", "renderPage", "styles", "nonce", "flush", "styledJsxInsertedHTML", "loadGetInitialProps", "__N_PREVIEW", "STATIC_PROPS_ID", "data", "getTracer", "trace", "RenderSpan", "spanName", "attributes", "draftMode", "preview", "revalidateReason", "isOnDemandRevalidate", "staticPropsError", "code", "GSP_NO_RETURNED_VALUE", "keys", "key", "UNSTABLE_REVALIDATE_RENAME_ERROR", "NODE_ENV", "notFound", "isNotFound", "__N_REDIRECT", "__N_REDIRECT_STATUS", "getRedirectStatus", "__N_REDIRECT_BASE_PATH", "isRedirect", "isSerializableProps", "Number", "isInteger", "Math", "ceil", "JSON", "stringify", "pageProps", "cacheControl", "pageData", "RenderResult", "SERVER_PROPS_ID", "canAccessRes", "resOrProxy", "deferred<PERSON><PERSON>nt", "Proxy", "get", "obj", "prop", "ReflectAdapter", "resolvedUrl", "serverSidePropsError", "isError", "GSSP_NO_RETURNED_VALUE", "Promise", "unstable_notFound", "unstable_redirect", "isResSent", "filteredBuildManifest", "page", "denormalizePagePath", "normalizePagePath", "pages", "lowPriorityFiles", "f", "Body", "div", "id", "renderDocument", "BuiltinFunctionalDocument", "NEXT_BUILTIN_DOCUMENT", "loadDocumentInitialProps", "renderShell", "EnhancedApp", "EnhancedComponent", "stream", "documentCtx", "docProps", "getDisplayName", "renderContent", "_App", "_Component", "content", "renderToInitialFizzStream", "ReactDOMServer", "hasDocumentGetInitialProps", "documentInitialPropsRes", "rawStyledJsxInsertedHTML", "all", "contentHTML", "documentElement", "htmlProps", "headTags", "setRootSpanAttribute", "documentResult", "dynamicImportsIds", "dynamicImports", "mod", "manifestItem", "add", "files", "for<PERSON>ach", "item", "hybridAmp", "doc<PERSON><PERSON><PERSON><PERSON><PERSON>ed", "assetPrefix", "disableOptimizedLoading", "runtimeConfig", "__NEXT_DATA__", "buildId", "autoExport", "dynamicIds", "size", "Array", "from", "gsp", "gssp", "customServer", "gip", "appGip", "strictNextHead", "dangerousAsPath", "canonicalBase", "isDevelopment", "dynamicCssManifest", "unstable_runtimeJS", "unstable_JsPreload", "crossOrigin", "optimizeCss", "nextScriptWorkers", "largePageDataBytes", "nextFontManifest", "experimentalClientTraceMetadata", "experimental", "clientTraceMetadata", "document", "HtmlContext", "documentHTML", "nonRenderedComponents", "expectedDocComponents", "comp", "missingComponentList", "e", "plural", "renderTargetPrefix", "renderTargetSuffix", "split", "prefix", "startsWith", "optimizedHtml"], "mappings": ";;;;;;;;;;;;;;;;IAuZgBA,WAAW;eAAXA;;IAurCHC,YAAY;eAAZA;;IAppCSC,gBAAgB;eAAhBA;;;;0BAraf;iCACyB;8DAmBA;4EACA;2BACmB;2BAU5C;4BAMA;qCAC6B;yBACR;yCACI;sBACJ;iDACO;8EACd;8CACW;4CACF;2BACC;uBAKxB;0CACqB;mCACM;qCACE;6BACL;gCACuB;qEACO;gEACzC;sCAIb;iDAC4B;kEACb;+BACe;0BAM9B;+CAC0B;iDAI1B;wBACmB;4BACC;yBACI;8BACO;6BACP;;;;;;AAI/B,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAEJ,MAAMC,UAAU;AAEhB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCN,oBACEO,QAAQ,yCAAyCP,iBAAiB;IACpEC,OAAOM,QAAQ,uBAAuBN,IAAI;IAC1CC,kBAAkBK,QAAQ,kBAAkBL,eAAe;AAC7D,OAAO;IACLD,OAAOO,QAAQP,IAAI,CAACQ,IAAI,CAACD;IACzBN,kBAAkB,OAAOQ,WAAmBC,OAAiBA;AAC/D;AAEA,SAASC;IACP,MAAMC,UACJ;IACF,MAAM,qBAAkB,CAAlB,IAAIC,MAAMD,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;AACzB;AAEA,eAAeE,eAAeC,OAA2B;IACvD,MAAMC,eAAe,MAAMC,4BAAmB,CAACC,sBAAsB,CAACH;IACtE,MAAMC,aAAaG,QAAQ;IAC3B,OAAOC,IAAAA,oCAAc,EAACJ;AACxB;AAEA,MAAMK;IAgBJC,YACEC,QAAgB,EAChBC,KAAqB,EACrBC,EAAU,EACV,EAAEC,UAAU,EAA2B,EACvCC,OAAgB,EAChBC,QAAgB,EAChBC,MAAe,EACfC,OAA2B,EAC3BC,aAAsB,EACtBC,aAAuC,EACvCC,SAAmB,EACnBC,cAAwB,CACxB;QACA,IAAI,CAACC,KAAK,GAAGZ,SAASa,OAAO,CAAC,OAAO,OAAO;QAC5C,IAAI,CAACb,QAAQ,GAAGA;QAChB,IAAI,CAACC,KAAK,GAAGA;QACb,IAAI,CAACa,MAAM,GAAGZ;QACd,IAAI,CAACC,UAAU,GAAGA;QAClB,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACC,MAAM,GAAGA;QACd,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACJ,OAAO,GAAGA;QACf,IAAI,CAACK,aAAa,GAAGA;QACrB,IAAI,CAACC,SAAS,GAAG,CAAC,CAACA;QACnB,IAAI,CAACC,cAAc,GAAG,CAAC,CAACA;IAC1B;IAEAI,OAAY;QACV3B;IACF;IACAyB,UAAe;QACbzB;IACF;IACA4B,SAAS;QACP5B;IACF;IACA6B,OAAO;QACL7B;IACF;IACA8B,UAAgB;QACd9B;IACF;IACA+B,WAAgB;QACd/B;IACF;IACAgC,iBAAiB;QACfhC;IACF;AACF;AAEA,SAASiC,kBACPC,OAA2B,EAC3BC,GAAY,EACZC,SAA4B;IAK5B,8BAA8B;IAC9B,IAAI,OAAOF,YAAY,YAAY;QACjC,OAAO;YACLC;YACAC,WAAWF,QAAQE;QACrB;IACF;IAEA,OAAO;QACLD,KAAKD,QAAQG,UAAU,GAAGH,QAAQG,UAAU,CAACF,OAAOA;QACpDC,WAAWF,QAAQI,gBAAgB,GAC/BJ,QAAQI,gBAAgB,CAACF,aACzBA;IACN;AACF;AAEA,SAASG,eACPJ,GAAY,EACZC,SAA4B,EAC5BI,KAAU;IAEV,qBAAO,qBAACL;QAAIC,WAAWA;QAAY,GAAGI,KAAK;;AAC7C;AAgHA,MAAMC,iBAAiB,CACrBC,YACAC;IAEA,MAAMC,eAAe,CAAC,QAAQ,EAAEF,WAAWG,iBAAiB,GAAG,MAAM,CAAC;IAEtE,OACE,CAAC,qCAAqC,EAAEH,WAAW,wFAAwF,CAAC,GAC5I,CAAC,6DAA6D,CAAC,GAC/D,CAAC,gCAAgC,EAAEC,YAAYG,IAAI,CAAC,MAAM,CAAC,CAAC,GAC5D,CAAC,8CAA8C,EAAEF,cAAc;AAEnE;AAEA,SAASG,oBACPC,QAAkB,EAClBC,GAAoB,EACpBC,MAA+C;IAE/C,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEpC,QAAQ,EAAE,GAAG+B;IACzD,IAAIM,SAAmB,EAAE;IAEzB,MAAMC,gBAAgB,OAAOF,eAAe;IAC5C,MAAMG,eAAe,OAAOJ,cAAc;IAE1C,IAAII,gBAAgBD,eAAe;QACjCD,OAAO3B,IAAI,CAAC,CAAC,yDAAyD,CAAC;IACzE,OAAO,IAAI6B,gBAAgB,OAAOJ,cAAc,WAAW;QACzDE,OAAO3B,IAAI,CAAC,CAAC,2CAA2C,CAAC;IAC3D,OAAO,IAAI4B,iBAAiB,CAACE,kCAAkB,CAACC,GAAG,CAACL,aAAc;QAChEC,OAAO3B,IAAI,CACT,CAAC,wCAAwC,EAAE;eAAI8B,kCAAkB;SAAC,CAACX,IAAI,CACrE,OACC;IAEP;IACA,MAAMa,kBAAkB,OAAOR;IAE/B,IAAIQ,oBAAoB,UAAU;QAChCL,OAAO3B,IAAI,CACT,CAAC,8CAA8C,EAAEgC,iBAAiB;IAEtE;IAEA,MAAMC,eAAe,OAAO3C;IAE5B,IAAI2C,iBAAiB,eAAeA,iBAAiB,WAAW;QAC9DN,OAAO3B,IAAI,CACT,CAAC,sDAAsD,EAAEiC,cAAc;IAE3E;IAEA,IAAIN,OAAOO,MAAM,GAAG,GAAG;QACrB,MAAM,qBAKL,CALK,IAAI3D,MACR,CAAC,sCAAsC,EAAEgD,OAAO,KAAK,EAAED,IAAIa,GAAG,CAAC,EAAE,CAAC,GAChER,OAAOR,IAAI,CAAC,WACZ,OACA,CAAC,0EAA0E,CAAC,GAJ1E,qBAAA;mBAAA;wBAAA;0BAAA;QAKN;IACF;AACF;AAEO,SAAS7D,YAAY8E,GAAU;IACpC,IAAIC,SACF;IAEF,IAAIxE,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvCsE,SAASC,IAAAA,2BAAc,EAACF,QAAQ;IAClC;IAEA,OAAO;QACLG,MAAMH,IAAIG,IAAI;QACdF;QACA/D,SAASkE,IAAAA,kBAAS,EAACJ,IAAI9D,OAAO;QAC9BmE,OAAOL,IAAIK,KAAK;QAChBC,QAAQ,AAACN,IAAYM,MAAM;IAC7B;AACF;AAEA,SAASC,eACPC,GAAwB,EACxBR,GAAU;IAKV,IAAIQ,KAAK;QACP,OAAOtF,YAAY8E;IACrB;IAEA,OAAO;QACLG,MAAM;QACNjE,SAAS;QACToD,YAAY;IACd;AACF;AAEO,eAAelE,iBACpB8D,GAAoB,EACpBuB,GAAmB,EACnB5D,QAAgB,EAChBC,KAAyB,EACzB4D,UAAmD,EACnDC,KAAsB,EACtBC,aAAiC,EACjCC,aAAiC;IAEjC,uEAAuE;IACvEC,IAAAA,qBAAW,EAAC;QAAE5B,KAAKA;IAAW,GAAG,WAAW6B,IAAAA,gCAAe,EAAC7B,IAAI8B,OAAO;IAEvE,MAAMC,WAAsC,CAAC;IAE7CA,SAASC,gBAAgB,GACvB,AAACR,WAAWF,GAAG,IAAIE,WAAWQ,gBAAgB,IAAK;IAErD,IAAIR,WAAWF,GAAG,IAAI,CAACS,SAASC,gBAAgB,EAAE;QAChD,MAAMC,YAAY,AAACjC,CAAAA,IAAI8B,OAAO,CAAC,aAAa,IAAI,EAAC,EAAGI,WAAW;QAC/D,IAAID,UAAUE,QAAQ,CAAC,aAAa,CAACF,UAAUE,QAAQ,CAAC,WAAW;YACjE,+EAA+E;YAC/E,4EAA4E;YAC5E,6FAA6F;YAC7F,yFAAyF;YACzF,iCAAiC;YACjCJ,SAASC,gBAAgB,GAAG,CAAC,IAAI,EAAEI,KAAKC,GAAG,IAAI;QACjD;IACF;IAEA,iEAAiE;IACjE,IAAIX,cAAcY,YAAY,EAAE;QAC9BP,SAASC,gBAAgB,IAAI,GAAGD,SAASC,gBAAgB,GAAG,MAAM,IAAI,IAAI,EACxEN,cAAcY,YAAY,EAC1B;IACJ;IAEA,qCAAqC;IACrC1E,QAAQ2E,OAAOC,MAAM,CAAC,CAAC,GAAG5E;IAE1B,MAAM,EACJkD,GAAG,EACHQ,MAAM,KAAK,EACXmB,UAAU,EAAE,EACZC,aAAa,CAAC,CAAC,EACfC,aAAa,EACbC,qBAAqB,EACrBC,UAAU,EACVC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBC,MAAM,EACNC,YAAY,EACZnF,QAAQ,EACRoF,MAAM,EACNC,SAASC,aAAa,EACtBC,qBAAqB,EACrBC,UAAU,EACX,GAAGhC;IACJ,MAAM,EAAEtC,GAAG,EAAE,GAAGuC;IAEhB,MAAMO,mBAAmBD,SAASC,gBAAgB;IAElD,IAAIyB,WAAWhC,MAAMgC,QAAQ;IAE7B,IAAItE,YACFqC,WAAWrC,SAAS;IACtB,MAAMuE,kBAAkBvE;IAExB,MAAMrB,aAAa6D,cAAc7D,UAAU,IAAI;IAC/C,MAAM6F,kBAAkBhC,cAAciC,6BAA6B;IAEnE,+CAA+C;IAC/CC,IAAAA,mCAAoB,EAACjG;IAErB,MAAMkG,QAAQ,CAAC,CAAChB;IAChB,MAAMiB,iBAAiBD,SAAStC,WAAWwC,UAAU;IACrD,MAAMC,4BACJ/E,IAAIgF,eAAe,KAAK,AAAChF,IAAYiF,mBAAmB;IAE1D,MAAMC,yBAAyB,CAAC,EAAEjF,6BAAD,AAACA,UAAmB+E,eAAe;IACpE,MAAMG,iBAAkBlF,6BAAD,AAACA,UAAmBmF,qBAAqB;IAEhE,MAAMC,gBAAgBC,IAAAA,yBAAc,EAAC7G;IAErC,MAAM8G,8BACJ9G,aAAa,aACb,AAACwB,UAAkB+E,eAAe,KAChC,AAAC/E,UAAkBgF,mBAAmB;IAE1C,IACE3C,WAAWwC,UAAU,IACrBI,0BACA,CAACK,6BACD;QACArI,KACE,CAAC,kCAAkC,EAAEuB,SAAS,CAAC,CAAC,GAC9C,CAAC,6DAA6D,CAAC,GAC/D,CAAC,wDAAwD,CAAC,GAC1D,CAAC,sEAAsE,CAAC;IAE9E;IAEA,IAAI+G,eACF,CAACN,0BACDH,6BACA,CAACH,SACD,CAACd;IAEH,2DAA2D;IAC3D,uDAAuD;IACvD,4DAA4D;IAC5D,gBAAgB;IAChB,IAAI0B,gBAAgB,CAACpD,OAAOiC,uBAAuB;QACjDhC,IAAIoD,SAAS,CACX,iBACAC,IAAAA,mCAAqB,EAAC;YAAEC,YAAY;YAAOC,QAAQtB;QAAW;QAEhEkB,eAAe;IACjB;IAEA,IAAIN,0BAA0BN,OAAO;QACnC,MAAM,qBAA0D,CAA1D,IAAI7G,MAAM8H,yCAA8B,GAAG,CAAC,CAAC,EAAEpH,UAAU,GAAzD,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,IAAIyG,0BAA0BpB,oBAAoB;QAChD,MAAM,qBAAgE,CAAhE,IAAI/F,MAAM+H,+CAAoC,GAAG,CAAC,CAAC,EAAErH,UAAU,GAA/D,qBAAA;mBAAA;wBAAA;0BAAA;QAA+D;IACvE;IAEA,IAAIqF,sBAAsBc,OAAO;QAC/B,MAAM,qBAAqD,CAArD,IAAI7G,MAAMgI,oCAAyB,GAAG,CAAC,CAAC,EAAEtH,UAAU,GAApD,qBAAA;mBAAA;wBAAA;0BAAA;QAAoD;IAC5D;IAEA,IAAIqF,sBAAsBxB,WAAW0D,gBAAgB,KAAK,UAAU;QAClE,MAAM,qBAEL,CAFK,IAAIjI,MACR,8IADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI8F,kBAAkB,CAACwB,eAAe;QACpC,MAAM,qBAGL,CAHK,IAAItH,MACR,CAAC,uEAAuE,EAAEU,SAAS,EAAE,CAAC,GACpF,CAAC,8EAA8E,CAAC,GAF9E,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAI,CAAC,CAACoF,kBAAkB,CAACe,OAAO;QAC9B,MAAM,qBAEL,CAFK,IAAI7G,MACR,CAAC,qDAAqD,EAAEU,SAAS,qDAAqD,CAAC,GADnH,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAImG,SAASS,iBAAiB,CAACxB,gBAAgB;QAC7C,MAAM,qBAGL,CAHK,IAAI9F,MACR,CAAC,qEAAqE,EAAEU,SAAS,EAAE,CAAC,GAClF,CAAC,0EAA0E,CAAC,GAF1E,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAIc,SAAiB+C,WAAW2D,cAAc,IAAKnF,IAAIa,GAAG;IAE1D,IAAIS,KAAK;QACP,MAAM,EAAE8D,kBAAkB,EAAE,GAAG1I,QAAQ;QACvC,IAAI,CAAC0I,mBAAmBjG,YAAY;YAClC,MAAM,qBAEL,CAFK,IAAIlC,MACR,CAAC,sDAAsD,EAAEU,SAAS,CAAC,CAAC,GADhE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACyH,mBAAmBlG,MAAM;YAC5B,MAAM,qBAEL,CAFK,IAAIjC,MACR,CAAC,4DAA4D,CAAC,GAD1D,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACmI,mBAAmB3B,WAAW;YACjC,MAAM,qBAEL,CAFK,IAAIxG,MACR,CAAC,iEAAiE,CAAC,GAD/D,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIyH,gBAAgB5G,YAAY;YAC9B,iEAAiE;YACjEF,QAAQ;gBACN,GAAIA,MAAMyH,GAAG,GACT;oBACEA,KAAKzH,MAAMyH,GAAG;gBAChB,IACA,CAAC,CAAC;YACR;YACA5G,SAAS,GAAGd,WACV,qEAAqE;YACrEqC,IAAIa,GAAG,CAAEyE,QAAQ,CAAC,QAAQ3H,aAAa,OAAO,CAAC4G,gBAAgB,MAAM,IACrE;YACFvE,IAAIa,GAAG,GAAGlD;QACZ;QAEA,IAAIA,aAAa,UAAWyG,CAAAA,0BAA0BpB,kBAAiB,GAAI;YACzE,MAAM,qBAEL,CAFK,IAAI/F,MACR,CAAC,cAAc,EAAEsI,qDAA0C,EAAE,GADzD,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IACEC,+BAAmB,CAACrD,QAAQ,CAACxE,aAC5ByG,CAAAA,0BAA0BpB,kBAAiB,GAC5C;YACA,MAAM,qBAEL,CAFK,IAAI/F,MACR,CAAC,OAAO,EAAEU,SAAS,GAAG,EAAE4H,qDAA0C,EAAE,GADhE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI/D,8BAAAA,WAAYiE,YAAY,EAAE;YAC5BjE,WAAWiE,YAAY,CAAChH,QAAQqF,SAASY,eAAe,OAAO;QACjE;IACF;IAEA,KAAK,MAAMjF,cAAc;QACvB;QACA;QACA;KACD,CAAE;QACD,IAAKN,6BAAD,AAACA,SAAmB,CAACM,WAAW,EAAE;YACpC,MAAM,qBAEL,CAFK,IAAIxC,MACR,CAAC,KAAK,EAAEU,SAAS,CAAC,EAAE8B,WAAW,CAAC,EAAEiG,sCAA2B,EAAE,GAD3D,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,MAAMC,8BAAQ,CAACC,UAAU,GAAG,2CAA2C;;IAEvE,IAAIvH,YAAiCwH;IACrC,IAAIC;IAEJ,IACE,AAAChC,CAAAA,SAASd,kBAAiB,KAC3B,CAAClF,cACDvB,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B0G,cACA;QACA,uEAAuE;QACvE,oEAAoE;QACpE,UAAU;QACV2C,cAAc3J,kBACZ6D,KACAuB,KACA4B,cACA,CAAC,CAAC3B,WAAWuE,kBAAkB;QAEjC1H,YAAYyH,gBAAgB;IAC9B;IAEA,yBAAyB;IACzB,MAAME,gBAAgB,CAAC,CACrBhD,CAAAA,sBACAoB,0BACC,CAACH,6BAA6B,CAACH,SAChCP,qBAAoB;IAEtB,MAAM0C,SAAS,IAAIxI,aACjBE,UACAC,OACAa,QACA;QACEX,YAAYA;IACd,GACAkI,eACAhI,UACAwD,WAAWvD,MAAM,EACjBuD,WAAWtD,OAAO,EAClBsD,WAAWrD,aAAa,EACxBqD,WAAWpD,aAAa,EACxBC,WACA6H,IAAAA,2BAAc,EAAClG,KAAK;IAGtB,MAAMmG,YAAYC,IAAAA,mCAAyB,EAACH;IAE5C,IAAII,eAAoB,CAAC;IACzB,MAAMC,mBAAmBC,IAAAA,8BAAmB;IAC5C,MAAMC,WAAW;QACfC,UAAU/D,WAAW2C,GAAG,KAAK;QAC7BqB,UAAUC,QAAQ/I,MAAMyH,GAAG;QAC3BuB,QAAQlE,WAAW2C,GAAG,KAAK;IAC7B;IAEA,wCAAwC;IACxC,MAAMwB,YAAYtK,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUqK,IAAAA,oBAAW,EAACN;IACrE,IAAIO,OAAsBC,IAAAA,iBAAW,EAACH;IACtC,MAAMI,uBAAiC,EAAE;IAEzC,IAAIC,iBAAsB,CAAC;IAC3B,IAAI7C,gBAAgB;QAClB6C,eAAeC,iBAAiB,GAAG,EAAE,CAClCC,MAAM,CAAC/C,kBACPgD,MAAM,CAAC,CAACC,SAAgBA,OAAO/H,KAAK,CAACgI,QAAQ,KAAK,qBAClDC,GAAG,CAAC,CAACF,SAAgBA,OAAO/H,KAAK;IACtC;IAEA,MAAMkI,eAAe,CAAC,EAAEC,QAAQ,EAA6B,iBAC3D,qBAACC,+CAAgB,CAACC,QAAQ;YAACC,OAAO1B;sBAChC,cAAA,qBAAC2B,oDAAmB,CAACF,QAAQ;gBAACC,OAAOE,IAAAA,8BAAoB,EAAC9B;0BACxD,cAAA,qBAAC+B,wCAA8B;oBAC7B/B,QAAQA;oBACRvB,cAAcA;8BAEd,cAAA,qBAACuD,kDAAiB,CAACL,QAAQ;wBAACC,OAAOK,IAAAA,4BAAkB,EAACjC;kCACpD,cAAA,qBAACkC,yCAAa,CAACP,QAAQ;4BAACC,OAAO5B;sCAC7B,cAAA,qBAACmC,wCAAe,CAACR,QAAQ;gCAACC,OAAOrB;0CAC/B,cAAA,qBAAC6B,mDAAkB,CAACT,QAAQ;oCAC1BC,OAAO;wCACLS,YAAY,CAACC;4CACXxB,OAAOwB;wCACT;wCACAC,eAAe,CAACC;4CACdpC,eAAeoC;wCACjB;wCACAA,SAASvB;wCACTwB,kBAAkB,IAAIC;oCACxB;8CAEA,cAAA,qBAACC,6CAAe,CAAChB,QAAQ;wCACvBC,OAAO,CAACgB,aACN5B,qBAAqBvI,IAAI,CAACmK;kDAG5B,cAAA,qBAACC,wBAAa;4CAACC,UAAUzC;sDACvB,cAAA,qBAAC0C,mDAAkB,CAACpB,QAAQ;gDAACC,OAAOzE;0DACjCsE;;;;;;;;;;;IAavB,yEAAyE;IACzE,4EAA4E;IAC5E,uDAAuD;IACvD,6EAA6E;IAC7E,iBAAiB;IACjB,+CAA+C;IAC/C,MAAMuB,OAAO,IAAM;IACnB,MAAMC,2CAED,CAAC,EAAExB,QAAQ,EAAE;QAChB,qBACE;;8BAEE,qBAACuB;8BACD,qBAACxB;8BACC,cAAA;;4BAEGnG,oBACC;;oCACGoG;kDACD,qBAACuB;;iCAGHvB;0CAGF,qBAACuB;;;;;;IAKX;IAEA,MAAME,MAAM;QACVrI;QACAd,KAAK0E,eAAemB,YAAY7F;QAChCuB,KAAKmD,eAAemB,YAAYtE;QAChC5D;QACAC;QACAa;QACAR,QAAQuD,WAAWvD,MAAM;QACzBC,SAASsD,WAAWtD,OAAO;QAC3BC,eAAeqD,WAAWrD,aAAa;QACvCiL,SAAS,CAAC7J;YACR,qBACE,qBAAC2J;0BACE5J,eAAeJ,KAAKwE,iBAAiB;oBAAE,GAAGnE,KAAK;oBAAE0G;gBAAO;;QAG/D;QACAoD,wBAAwB,OACtBC,QACArK,UAA8B,CAAC,CAAC;YAEhC,MAAMG,aAAa,CAACmK;gBAClB,OAAO,CAAChK,sBAAe,qBAACgK;wBAAS,GAAGhK,KAAK;;YAC3C;YAEA,MAAM,EAAEzC,IAAI,EAAEiK,MAAMyC,cAAc,EAAE,GAAG,MAAMF,OAAOG,UAAU,CAAC;gBAC7DrK;YACF;YACA,MAAMsK,SAASpD,iBAAiBoD,MAAM,CAAC;gBAAEC,OAAO1K,QAAQ0K,KAAK;YAAC;YAC9DrD,iBAAiBsD,KAAK;YACtB,OAAO;gBAAE9M;gBAAMiK,MAAMyC;gBAAgBE;YAAO;QAC9C;IACF;IACA,IAAInK;IAEJ,MAAMyE,aACJ,CAACF,SAAUtC,CAAAA,WAAWwC,UAAU,IAAK1C,OAAQoD,CAAAA,gBAAgB5G,UAAS,CAAE;IAE1E,MAAM+L,wBAAwB;QAC5B,MAAMH,SAASpD,iBAAiBoD,MAAM;QACtCpD,iBAAiBsD,KAAK;QACtB,qBAAO;sBAAGF;;IACZ;IAEAnK,QAAQ,MAAMuK,IAAAA,0BAAmB,EAAC5K,KAAK;QACrCkK,SAASD,IAAIC,OAAO;QACpBjK;QACA8G;QACAkD;IACF;IAEA,IAAI,AAACrF,CAAAA,SAASd,kBAAiB,KAAM3E,WAAW;QAC9CkB,MAAMwK,WAAW,GAAG;IACtB;IAEA,IAAIjG,OAAO;QACTvE,KAAK,CAACyK,2BAAe,CAAC,GAAG;IAC3B;IAEA,IAAIlG,SAAS,CAAChG,YAAY;QACxB,IAAImM;QAEJ,IAAI;YACFA,OAAO,MAAMC,IAAAA,iBAAS,IAAGC,KAAK,CAC5BC,sBAAU,CAACtH,cAAc,EACzB;gBACEuH,UAAU,CAAC,eAAe,EAAE1M,UAAU;gBACtC2M,YAAY;oBACV,cAAc3M;gBAChB;YACF,GACA,IACEmF,eAAe;oBACb,GAAIyB,gBAAgB;wBAAErB;oBAAO,IAAI2C,SAAS;oBAC1C,GAAIxH,YACA;wBAAEkM,WAAW;wBAAMC,SAAS;wBAAM1E,aAAaA;oBAAY,IAC3DD,SAAS;oBACb3H,SAAS;2BAAKsD,WAAWtD,OAAO,IAAI,EAAE;qBAAE;oBACxCD,QAAQuD,WAAWvD,MAAM;oBACzBE,eAAeqD,WAAWrD,aAAa;oBACvCsM,kBAAkBjJ,WAAWkJ,oBAAoB,GAC7C,cACA3G,iBACE,UACA;gBACR;QAEN,EAAE,OAAO4G,kBAAuB;YAC9B,2DAA2D;YAC3D,gBAAgB;YAChB,IAAIA,oBAAoBA,iBAAiBC,IAAI,KAAK,UAAU;gBAC1D,OAAOD,iBAAiBC,IAAI;YAC9B;YACA,MAAMD;QACR;QAEA,IAAIV,QAAQ,MAAM;YAChB,MAAM,qBAAgC,CAAhC,IAAIhN,MAAM4N,gCAAqB,GAA/B,qBAAA;uBAAA;4BAAA;8BAAA;YAA+B;QACvC;QAEA,MAAMnL,cAAc6C,OAAOuI,IAAI,CAACb,MAAM5C,MAAM,CAC1C,CAAC0D,MACCA,QAAQ,gBACRA,QAAQ,WACRA,QAAQ,cACRA,QAAQ;QAGZ,IAAIrL,YAAYyC,QAAQ,CAAC,wBAAwB;YAC/C,MAAM,qBAA2C,CAA3C,IAAIlF,MAAM+N,2CAAgC,GAA1C,qBAAA;uBAAA;4BAAA;8BAAA;YAA0C;QAClD;QAEA,IAAItL,YAAYkB,MAAM,EAAE;YACtB,MAAM,qBAAwD,CAAxD,IAAI3D,MAAMuC,eAAe,kBAAkBE,eAA3C,qBAAA;uBAAA;4BAAA;8BAAA;YAAuD;QAC/D;QAEA,IAAInD,QAAQC,GAAG,CAACyO,QAAQ,KAAK,cAAc;YACzC,IACE,OAAO,AAAChB,KAAaiB,QAAQ,KAAK,eAClC,OAAO,AAACjB,KAAalK,QAAQ,KAAK,aAClC;gBACA,MAAM,qBAIL,CAJK,IAAI9C,MACR,CAAC,4DAA4D,EAC3D6G,QAAQ,mBAAmB,qBAC5B,yBAAyB,EAAEnG,SAAS,oFAAoF,CAAC,GAHtH,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;QACF;QAEA,IAAI,cAAcsM,QAAQA,KAAKiB,QAAQ,EAAE;YACvC,IAAIvN,aAAa,QAAQ;gBACvB,MAAM,qBAEL,CAFK,IAAIV,MACR,CAAC,wFAAwF,CAAC,GADtF,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA8E,SAASoJ,UAAU,GAAG;QACxB;QAEA,IACE,cAAclB,QACdA,KAAKlK,QAAQ,IACb,OAAOkK,KAAKlK,QAAQ,KAAK,UACzB;YACAD,oBAAoBmK,KAAKlK,QAAQ,EAAcC,KAAK;YAEpD,IAAI+D,gBAAgB;gBAClB,MAAM,qBAGL,CAHK,IAAI9G,MACR,CAAC,0EAA0E,EAAE+C,IAAIa,GAAG,CAAC,GAAG,CAAC,GACvF,CAAC,kFAAkF,CAAC,GAFlF,qBAAA;2BAAA;gCAAA;kCAAA;gBAGN;YACF;;YAEEoJ,KAAa1K,KAAK,GAAG;gBACrB6L,cAAcnB,KAAKlK,QAAQ,CAACG,WAAW;gBACvCmL,qBAAqBC,IAAAA,iCAAiB,EAACrB,KAAKlK,QAAQ;YACtD;YACA,IAAI,OAAOkK,KAAKlK,QAAQ,CAAC/B,QAAQ,KAAK,aAAa;;gBAC/CiM,KAAa1K,KAAK,CAACgM,sBAAsB,GAAGtB,KAAKlK,QAAQ,CAAC/B,QAAQ;YACtE;YACA+D,SAASyJ,UAAU,GAAG;QACxB;QAEA,IACE,AAAClK,CAAAA,OAAOyC,cAAa,KACrB,CAAChC,SAASoJ,UAAU,IACpB,CAACM,IAAAA,wCAAmB,EAAC9N,UAAU,kBAAkB,AAACsM,KAAa1K,KAAK,GACpE;YACA,kEAAkE;YAClE,MAAM,qBAEL,CAFK,IAAItC,MACR,8EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI4H;QACJ,IAAI,gBAAgBoF,MAAM;YACxB,IAAIA,KAAKpF,UAAU,IAAIrD,WAAW0D,gBAAgB,KAAK,UAAU;gBAC/D,MAAM,qBAEL,CAFK,IAAIjI,MACR,+HADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,IAAI,OAAOgN,KAAKpF,UAAU,KAAK,UAAU;gBACvC,IAAI,CAAC6G,OAAOC,SAAS,CAAC1B,KAAKpF,UAAU,GAAG;oBACtC,MAAM,qBAKL,CALK,IAAI5H,MACR,CAAC,6EAA6E,EAAE+C,IAAIa,GAAG,CAAC,0BAA0B,EAAEoJ,KAAKpF,UAAU,CAAC,kBAAkB,CAAC,GACrJ,CAAC,6BAA6B,EAAE+G,KAAKC,IAAI,CACvC5B,KAAKpF,UAAU,EACf,yDAAyD,CAAC,GAJ1D,qBAAA;+BAAA;oCAAA;sCAAA;oBAKN;gBACF,OAAO,IAAIoF,KAAKpF,UAAU,IAAI,GAAG;oBAC/B,MAAM,qBAIL,CAJK,IAAI5H,MACR,CAAC,qEAAqE,EAAE+C,IAAIa,GAAG,CAAC,oHAAoH,CAAC,GACnM,CAAC,2FAA2F,CAAC,GAC7F,CAAC,oEAAoE,CAAC,GAHpE,qBAAA;+BAAA;oCAAA;sCAAA;oBAIN;gBACF,OAAO;oBACL,IAAIoJ,KAAKpF,UAAU,GAAG,UAAU;wBAC9B,oDAAoD;wBACpDlI,QAAQP,IAAI,CACV,CAAC,oEAAoE,EAAE4D,IAAIa,GAAG,CAAC,mCAAmC,CAAC,GACjH,CAAC,kHAAkH,CAAC;oBAE1H;oBAEAgE,aAAaoF,KAAKpF,UAAU;gBAC9B;YACF,OAAO,IAAIoF,KAAKpF,UAAU,KAAK,MAAM;gBACnC,qEAAqE;gBACrE,0DAA0D;gBAC1D,yBAAyB;gBACzBA,aAAa;YACf,OAAO,IACLoF,KAAKpF,UAAU,KAAK,SACpB,OAAOoF,KAAKpF,UAAU,KAAK,aAC3B;gBACA,mCAAmC;gBACnCA,aAAa;YACf,OAAO;gBACL,MAAM,qBAIL,CAJK,IAAI5H,MACR,CAAC,8HAA8H,EAAE6O,KAAKC,SAAS,CAC7I9B,KAAKpF,UAAU,EACf,MAAM,EAAE7E,IAAIa,GAAG,EAAE,GAHf,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;QACF,OAAO;YACL,mCAAmC;YACnCgE,aAAa;QACf;QAEAtF,MAAMyM,SAAS,GAAGzJ,OAAOC,MAAM,CAC7B,CAAC,GACDjD,MAAMyM,SAAS,EACf,WAAW/B,OAAOA,KAAK1K,KAAK,GAAGsG;QAGjC,6CAA6C;QAC7C9D,SAASkK,YAAY,GAAG;YAAEpH;YAAYC,QAAQe;QAAU;QACxD9D,SAASmK,QAAQ,GAAG3M;QAEpB,+DAA+D;QAC/D,IAAIwC,SAASoJ,UAAU,EAAE;YACvB,OAAO,IAAIgB,qBAAY,CAAC,MAAM;gBAAEpK;YAAS;QAC3C;IACF;IAEA,IAAIiB,oBAAoB;QACtBzD,KAAK,CAAC6M,2BAAe,CAAC,GAAG;IAC3B;IAEA,IAAIpJ,sBAAsB,CAAClF,YAAY;QACrC,IAAImM;QAEJ,IAAIoC,eAAe;QACnB,IAAIC,aAAa/K;QACjB,IAAIgL,kBAAkB;QACtB,IAAIhQ,QAAQC,GAAG,CAACyO,QAAQ,KAAK,cAAc;YACzCqB,aAAa,IAAIE,MAAsBjL,KAAK;gBAC1CkL,KAAK,SAAUC,GAAG,EAAEC,IAAI;oBACtB,IAAI,CAACN,cAAc;wBACjB,MAAMrP,UACJ,CAAC,8DAA8D,CAAC,GAChE,CAAC,kEAAkE,CAAC;wBAEtE,IAAIuP,iBAAiB;4BACnB,MAAM,qBAAkB,CAAlB,IAAItP,MAAMD,UAAV,qBAAA;uCAAA;4CAAA;8CAAA;4BAAiB;wBACzB,OAAO;4BACLZ,KAAKY;wBACP;oBACF;oBAEA,IAAI,OAAO2P,SAAS,UAAU;wBAC5B,OAAOC,uBAAc,CAACH,GAAG,CAACC,KAAKC,MAAMpL;oBACvC;oBAEA,OAAOqL,uBAAc,CAACH,GAAG,CAACC,KAAKC,MAAMpL;gBACvC;YACF;QACF;QAEA,IAAI;YACF0I,OAAO,MAAMC,IAAAA,iBAAS,IAAGC,KAAK,CAC5BC,sBAAU,CAACpH,kBAAkB,EAC7B;gBACEqH,UAAU,CAAC,mBAAmB,EAAE1M,UAAU;gBAC1C2M,YAAY;oBACV,cAAc3M;gBAChB;YACF,GACA,UACEqF,mBAAmB;oBACjBhD,KAAKA;oBAGLuB,KAAK+K;oBACL1O;oBACAiP,aAAarL,WAAWqL,WAAW;oBACnC,GAAItI,gBAAgB;wBAAErB;oBAAO,IAAI2C,SAAS;oBAC1C,GAAIC,gBAAgB,QAChB;wBAAEyE,WAAW;wBAAMC,SAAS;wBAAM1E,aAAaA;oBAAY,IAC3DD,SAAS;oBACb,qDAAqD;oBACrD,6DAA6D;oBAC7D,sDAAsD;oBACtD3H,SAAS;2BAAKsD,WAAWtD,OAAO,IAAI,EAAE;qBAAE;oBACxCD,QAAQuD,WAAWvD,MAAM;oBACzBE,eAAeqD,WAAWrD,aAAa;gBACzC;YAEJkO,eAAe;YACftK,SAASkK,YAAY,GAAG;gBAAEpH,YAAY;gBAAGC,QAAQe;YAAU;QAC7D,EAAE,OAAOiH,sBAA2B;YAClC,2DAA2D;YAC3D,gBAAgB;YAChB,IACEC,IAAAA,gBAAO,EAACD,yBACRA,qBAAqBlC,IAAI,KAAK,UAC9B;gBACA,OAAOkC,qBAAqBlC,IAAI;YAClC;YACA,MAAMkC;QACR;QAEA,IAAI7C,QAAQ,MAAM;YAChB,MAAM,qBAAiC,CAAjC,IAAIhN,MAAM+P,iCAAsB,GAAhC,qBAAA;uBAAA;4BAAA;8BAAA;YAAgC;QACxC;QAEA,IAAI,AAAC/C,KAAa1K,KAAK,YAAY0N,SAAS;YAC1CV,kBAAkB;QACpB;QAEA,MAAM7M,cAAc6C,OAAOuI,IAAI,CAACb,MAAM5C,MAAM,CAC1C,CAAC0D,MAAQA,QAAQ,WAAWA,QAAQ,cAAcA,QAAQ;QAG5D,IAAI,AAACd,KAAaiD,iBAAiB,EAAE;YACnC,MAAM,qBAEL,CAFK,IAAIjQ,MACR,CAAC,2FAA2F,EAAEU,UAAU,GADpG,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAI,AAACsM,KAAakD,iBAAiB,EAAE;YACnC,MAAM,qBAEL,CAFK,IAAIlQ,MACR,CAAC,2FAA2F,EAAEU,UAAU,GADpG,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI+B,YAAYkB,MAAM,EAAE;YACtB,MAAM,qBAA4D,CAA5D,IAAI3D,MAAMuC,eAAe,sBAAsBE,eAA/C,qBAAA;uBAAA;4BAAA;8BAAA;YAA2D;QACnE;QAEA,IAAI,cAAcuK,QAAQA,KAAKiB,QAAQ,EAAE;YACvC,IAAIvN,aAAa,QAAQ;gBACvB,MAAM,qBAEL,CAFK,IAAIV,MACR,CAAC,wFAAwF,CAAC,GADtF,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA8E,SAASoJ,UAAU,GAAG;YACtB,OAAO,IAAIgB,qBAAY,CAAC,MAAM;gBAAEpK;YAAS;QAC3C;QAEA,IAAI,cAAckI,QAAQ,OAAOA,KAAKlK,QAAQ,KAAK,UAAU;YAC3DD,oBAAoBmK,KAAKlK,QAAQ,EAAcC,KAAK;YAClDiK,KAAa1K,KAAK,GAAG;gBACrB6L,cAAcnB,KAAKlK,QAAQ,CAACG,WAAW;gBACvCmL,qBAAqBC,IAAAA,iCAAiB,EAACrB,KAAKlK,QAAQ;YACtD;YACA,IAAI,OAAOkK,KAAKlK,QAAQ,CAAC/B,QAAQ,KAAK,aAAa;;gBAC/CiM,KAAa1K,KAAK,CAACgM,sBAAsB,GAAGtB,KAAKlK,QAAQ,CAAC/B,QAAQ;YACtE;YACA+D,SAASyJ,UAAU,GAAG;QACxB;QAEA,IAAIe,iBAAiB;;YACjBtC,KAAa1K,KAAK,GAAG,MAAM,AAAC0K,KAAa1K,KAAK;QAClD;QAEA,IACE,AAAC+B,CAAAA,OAAOyC,cAAa,KACrB,CAAC0H,IAAAA,wCAAmB,EAAC9N,UAAU,sBAAsB,AAACsM,KAAa1K,KAAK,GACxE;YACA,kEAAkE;YAClE,MAAM,qBAEL,CAFK,IAAItC,MACR,kFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAsC,MAAMyM,SAAS,GAAGzJ,OAAOC,MAAM,CAAC,CAAC,GAAGjD,MAAMyM,SAAS,EAAE,AAAC/B,KAAa1K,KAAK;QACxEwC,SAASmK,QAAQ,GAAG3M;IACtB;IAEA,IACE,CAACuE,SAAS,6CAA6C;IACvD,CAACd,sBACDzG,QAAQC,GAAG,CAACyO,QAAQ,KAAK,gBACzB1I,OAAOuI,IAAI,CAACvL,CAAAA,yBAAAA,MAAOyM,SAAS,KAAI,CAAC,GAAG7J,QAAQ,CAAC,QAC7C;QACAxF,QAAQP,IAAI,CACV,CAAC,iGAAiG,EAAEuB,SAAS,EAAE,CAAC,GAC9G,CAAC,uEAAuE,CAAC;IAE/E;IAEA,0EAA0E;IAC1E,kDAAkD;IAClD,IAAI,AAACsF,qBAAqB,CAACa,SAAU/B,SAASyJ,UAAU,EAAE;QACxD,OAAO,IAAIW,qBAAY,CAACL,KAAKC,SAAS,CAACxM,QAAQ;YAC7CwC;QACF;IACF;IAEA,sEAAsE;IACtE,gEAAgE;IAChE,IAAIjE,YAAY;QACdyB,MAAMyM,SAAS,GAAG,CAAC;IACrB;IAEA,6DAA6D;IAC7D,IAAIoB,IAAAA,gBAAS,EAAC7L,QAAQ,CAACuC,OAAO,OAAO,IAAIqI,qBAAY,CAAC,MAAM;QAAEpK;IAAS;IAEvE,6DAA6D;IAC7D,qCAAqC;IACrC,IAAIsL,wBAAwB1K;IAC5B,IAAI+B,gBAAgBH,eAAe;QACjC,MAAM+I,OAAOC,IAAAA,wCAAmB,EAACC,IAAAA,oCAAiB,EAAC7P;QACnD,0EAA0E;QAC1E,sEAAsE;QACtE,UAAU;QACV,IAAI2P,QAAQD,sBAAsBI,KAAK,EAAE;YACvCJ,wBAAwB;gBACtB,GAAGA,qBAAqB;gBACxBI,OAAO;oBACL,GAAGJ,sBAAsBI,KAAK;oBAC9B,CAACH,KAAK,EAAE;2BACHD,sBAAsBI,KAAK,CAACH,KAAK;2BACjCD,sBAAsBK,gBAAgB,CAACrG,MAAM,CAAC,CAACsG,IAChDA,EAAExL,QAAQ,CAAC;qBAEd;gBACH;gBACAuL,kBAAkBL,sBAAsBK,gBAAgB,CAACrG,MAAM,CAC7D,CAACsG,IAAM,CAACA,EAAExL,QAAQ,CAAC;YAEvB;QACF;IACF;IAEA,MAAMyL,OAAO,CAAC,EAAElG,QAAQ,EAA6B;QACnD,OAAOb,YAAYa,yBAAW,qBAACmG;YAAIC,IAAG;sBAAUpG;;IAClD;IAEA,MAAMqG,iBAAiB;QACrB,6DAA6D;QAC7D,2DAA2D;QAC3D,oEAAoE;QAEpE,MAAMC,4BAAsD,AAC1DvK,QACD,CAACwK,iCAAqB,CAAC;QAExB,IAAI1R,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUgH,SAASS,eAAe,EAAE;YACnE,mEAAmE;YACnE,6CAA6C;YAC7C,IAAI8J,2BAA2B;gBAC7BvK,WAAWuK;YACb,OAAO;gBACL,MAAM,qBAEL,CAFK,IAAI/Q,MACR,oFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,eAAeiR,yBACbC,WAGiC;YAEjC,MAAM1E,aAAyB,OAC7BxK,UAA8B,CAAC,CAAC;gBAEhC,IAAIkK,IAAIrI,GAAG,IAAI+B,YAAY;oBACzB,6DAA6D;oBAC7D,IAAIsL,aAAa;wBACfA,YAAYjP,KAAKC;oBACnB;oBAEA,MAAMrC,OAAO,MAAMI,6BACjB,qBAAC0Q;kCACC,cAAA,qBAAC/K;;oBAGL,OAAO;wBAAE/F;wBAAMiK;oBAAK;gBACtB;gBAEA,IAAIzF,OAAQ/B,CAAAA,MAAM0G,MAAM,IAAI1G,MAAMJ,SAAS,AAAD,GAAI;oBAC5C,MAAM,qBAEL,CAFK,IAAIlC,MACR,CAAC,sIAAsI,CAAC,GADpI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,MAAM,EAAEiC,KAAKkP,WAAW,EAAEjP,WAAWkP,iBAAiB,EAAE,GACtDrP,kBAAkBC,SAASC,KAAKC;gBAElC,MAAMmP,SAAS,MAAMH,YAAYC,aAAaC;gBAC9C,MAAMC,OAAO/Q,QAAQ;gBACrB,MAAMT,OAAO,MAAMU,IAAAA,oCAAc,EAAC8Q;gBAElC,OAAO;oBAAExR;oBAAMiK;gBAAK;YACtB;YACA,MAAMwH,cAAc;gBAAE,GAAGpF,GAAG;gBAAEM;YAAW;YACzC,MAAM+E,WAAiC,MAAM1E,IAAAA,0BAAmB,EAC9DrG,UACA8K;YAEF,6DAA6D;YAC7D,IAAInB,IAAAA,gBAAS,EAAC7L,QAAQ,CAACuC,OAAO,OAAO;YAErC,IAAI,CAAC0K,YAAY,OAAOA,SAAS1R,IAAI,KAAK,UAAU;gBAClD,MAAME,UAAU,CAAC,CAAC,EAAEyR,IAAAA,qBAAc,EAChChL,UACA,+FAA+F,CAAC;gBAClG,MAAM,qBAAkB,CAAlB,IAAIxG,MAAMD,UAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiB;YACzB;YAEA,OAAO;gBAAEwR;gBAAUD;YAAY;QACjC;QAEA,MAAMG,gBAAgB,CAACC,MAAeC;YACpC,MAAMR,cAAcO,QAAQzP;YAC5B,MAAMmP,oBAAoBO,cAAczP;YAExC,OAAOgK,IAAIrI,GAAG,IAAI+B,2BAChB,qBAAC+K;0BACC,cAAA,qBAAC/K;+BAGH,qBAAC+K;0BACC,cAAA,qBAAC1E;8BACE5J,eAAe8O,aAAaC,mBAAmB;wBAC9C,GAAG9O,KAAK;wBACR0G;oBACF;;;QAIR;QAEA,gFAAgF;QAChF,MAAMkI,cAAc,OAClBC,aACAC;YAEA,MAAMQ,UAAUH,cAAcN,aAAaC;YAC3C,OAAO,MAAMS,IAAAA,+CAAyB,EAAC;gBACrCC,gBAAgB1R,4BAAmB;gBACnCF,SAAS0R;YACX;QACF;QAEA,MAAMG,6BACJzS,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAU,CAAC,CAACgH,SAASS,eAAe;QAEnE,uEAAuE;QACvE,gCAAgC;QAChC,IAAI+K;QAIJ,MAAM,CAACC,0BAA0BL,QAAQ,GAAG,MAAM5B,QAAQkC,GAAG,CAAC;YAC5DjS,eAAe2M;YACd,CAAA;gBACC,IAAImF,4BAA4B;oBAC9BC,0BAA0B,MAAMf,yBAAyBC;oBACzD,IAAIc,4BAA4B,MAAM,OAAO;oBAC7C,MAAM,EAAET,QAAQ,EAAE,GAAGS;oBACrB,OAAOT,SAAS1R,IAAI;gBACtB,OAAO;oBACLmS,0BAA0B,CAAC;oBAC3B,MAAMX,SAAS,MAAMH,YAAYjP,KAAKC;oBACtC,MAAMmP,OAAO/Q,QAAQ;oBACrB,OAAOC,IAAAA,oCAAc,EAAC8Q;gBACxB;YACF,CAAA;SACD;QAED,IAAIO,YAAY,MAAM;YACpB,OAAO;QACT;QAEA,MAAMO,cAAcF,2BAA2BL;QAE/C,6CAA6C;QAC7C,MAAM,EAAEL,QAAQ,EAAE,GAAG,AAACS,2BAAmC,CAAC;QAC1D,MAAMI,kBAAkB,CAACC;YACvB,IAAI/S,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;gBACvC,OAAO,AAACgH;YACV,OAAO;gBACL,qBAAO,qBAACA;oBAAU,GAAG6L,SAAS;oBAAG,GAAGd,QAAQ;;YAC9C;QACF;QAEA,IAAI9E;QACJ,IAAIsF,4BAA4B;YAC9BtF,SAAS8E,SAAS9E,MAAM;YACxB3C,OAAOyH,SAASzH,IAAI;QACtB,OAAO;YACL2C,SAASpD,iBAAiBoD,MAAM;YAChCpD,iBAAiBsD,KAAK;QACxB;QAEA,OAAO;YACLwF;YACAC;YACAtI;YACAwI,UAAU,EAAE;YACZ7F;QACF;IACF;IAEAQ,IAAAA,iBAAS,IAAGsF,oBAAoB,CAAC,cAAchO,WAAW8L,IAAI;IAC9D,MAAMmC,iBAAiB,MAAMvF,IAAAA,iBAAS,IAAGC,KAAK,CAC5CC,sBAAU,CAAC2D,cAAc,EACzB;QACE1D,UAAU,CAAC,qBAAqB,EAAE7I,WAAW8L,IAAI,EAAE;QACnDhD,YAAY;YACV,cAAc9I,WAAW8L,IAAI;QAC/B;IACF,GACA,UAAYS;IAEd,IAAI,CAAC0B,gBAAgB;QACnB,OAAO,IAAItD,qBAAY,CAAC,MAAM;YAAEpK;QAAS;IAC3C;IAEA,MAAM2N,oBAAoB,IAAI/G;IAC9B,MAAMgH,iBAAiB,IAAIhH;IAE3B,KAAK,MAAMiH,OAAO3I,qBAAsB;QACtC,MAAM4I,eAAejN,qBAAqB,CAACgN,IAAI;QAE/C,IAAIC,cAAc;YAChBH,kBAAkBI,GAAG,CAACD,aAAa/B,EAAE;YACrC+B,aAAaE,KAAK,CAACC,OAAO,CAAC,CAACC;gBAC1BN,eAAeG,GAAG,CAACG;YACrB;QACF;IACF;IAEA,MAAMC,YAAY1J,SAASI,MAAM;IACjC,MAAMuJ,wBAAgE,CAAC;IAEvE,MAAM,EACJC,WAAW,EACXjS,aAAa,EACbkS,uBAAuB,EACvBjS,aAAa,EACbH,MAAM,EACNC,OAAO,EACPoS,aAAa,EACd,GAAG9O;IACJ,MAAM8N,YAAuB;QAC3BiB,eAAe;YACbhR;YACA+N,MAAM3P;YACNC;YACA4S,SAAS9O,cAAc8O,OAAO;YAC9BJ,aAAaA,gBAAgB,KAAKvK,YAAYuK;YAC9CE;YACAtM,YAAYA,eAAe,OAAO,OAAO6B;YACzC4K,YAAY/L,iBAAiB,OAAO,OAAOmB;YAC3C/H;YACAyF;YACAmN,YACEhB,kBAAkBiB,IAAI,KAAK,IACvB9K,YACA+K,MAAMC,IAAI,CAACnB;YACjB5O,KAAKU,WAAWV,GAAG,GAAGO,eAAeC,KAAKE,WAAWV,GAAG,IAAI+E;YAC5DiL,KAAK,CAAC,CAAChO,iBAAiB,OAAO+C;YAC/BkL,MAAM,CAAC,CAAC/N,qBAAqB,OAAO6C;YACpCmL,cAActP,cAAcsP,YAAY;YACxCC,KAAK7M,yBAAyB,OAAOyB;YACrCqL,QAAQ,CAACjN,4BAA4B,OAAO4B;YAC5C5H;YACAC;YACAC;YACAC;YACAC,WAAWA,cAAc,OAAO,OAAOwH;YACvClC,iBAAiBA,mBAAmBrC,MAAMqC,kBAAkBkC;QAC9D;QACAsL,gBAAgB3P,WAAW2P,cAAc;QACzCxO,eAAe0K;QACf8C;QACAiB,iBAAiBnL,OAAOxH,MAAM;QAC9B4S,eACE,CAAC7P,WAAWiB,OAAO,IAAIyD,IAAAA,2BAAc,EAAClG,KAAK,oBACvC,GAAGwB,WAAW6P,aAAa,IAAI,GAAG,CAAC,EAAE7P,WAAWvD,MAAM,EAAE,GACxDuD,WAAW6P,aAAa;QAC9B5O;QACAoE;QACAyK,eAAe,CAAC,CAAChQ;QACjB4O;QACAP,gBAAgBiB,MAAMC,IAAI,CAAClB;QAC3B4B,oBAAoB,IAAI5I,IAAInH,WAAW+P,kBAAkB,IAAI,EAAE;QAC/DnB;QACA,2GAA2G;QAC3GoB,oBACEjV,QAAQC,GAAG,CAACyO,QAAQ,KAAK,eACrBvI,WAAW8O,kBAAkB,GAC7B3L;QACN4L,oBAAoB/O,WAAW+O,kBAAkB;QACjDzP;QACAqE;QACApI;QACAoS;QACAtJ,MAAM0I,eAAe1I,IAAI;QACzBwI,UAAUE,eAAeF,QAAQ;QACjC7F,QAAQ+F,eAAe/F,MAAM;QAC7BgI,aAAalQ,WAAWkQ,WAAW;QACnCC,aAAanQ,WAAWmQ,WAAW;QACnCzM,kBAAkB1D,WAAW0D,gBAAgB;QAC7C0M,mBAAmBpQ,WAAWoQ,iBAAiB;QAC/CvO,SAASC;QACTuO,oBAAoBrQ,WAAWqQ,kBAAkB;QACjDC,kBAAkBtQ,WAAWsQ,gBAAgB;QAC7CC,iCACEvQ,WAAWwQ,YAAY,CAACC,mBAAmB;IAC/C;IAEA,MAAMC,yBACJ,qBAAC9J,wCAAe,CAACR,QAAQ;QAACC,OAAOrB;kBAC/B,cAAA,qBAAC2L,qCAAW,CAACvK,QAAQ;YAACC,OAAOyH;sBAC1BG,eAAeJ,eAAe,CAACC;;;IAKtC,MAAM8C,eAAe,MAAMlI,IAAAA,iBAAS,IAAGC,KAAK,CAC1CC,sBAAU,CAAClN,cAAc,EACzB,UAAYA,eAAegV;IAG7B,IAAI3V,QAAQC,GAAG,CAACyO,QAAQ,KAAK,cAAc;QACzC,MAAMoH,wBAAwB,EAAE;QAChC,MAAMC,wBAAwB;YAAC;YAAQ;YAAQ;YAAc;SAAO;QAEpE,KAAK,MAAMC,QAAQD,sBAAuB;YACxC,IAAI,CAAC,AAACnC,qBAA6B,CAACoC,KAAK,EAAE;gBACzCF,sBAAsB3T,IAAI,CAAC6T;YAC7B;QACF;QAEA,IAAIF,sBAAsBzR,MAAM,EAAE;YAChC,MAAM4R,uBAAuBH,sBAC1B7K,GAAG,CAAC,CAACiL,IAAM,CAAC,CAAC,EAAEA,EAAE,GAAG,CAAC,EACrB5S,IAAI,CAAC;YACR,MAAM6S,SAASL,sBAAsBzR,MAAM,KAAK,IAAI,MAAM;YAC1DjE,QAAQP,IAAI,CACV,CAAC,mFAAmF,EAAEsW,OAAO,GAAG,CAAC,GAC/F,CAAC,iBAAiB,EAAEA,OAAO,EAAE,EAAEF,qBAAqB,EAAE,CAAC,GACvD;QAEN;IACF;IAEA,MAAM,CAACG,oBAAoBC,mBAAmB,GAAGR,aAAaS,KAAK,CACjE,+EACA;IAGF,IAAIC,SAAS;IACb,IAAI,CAACV,aAAaW,UAAU,CAACzW,UAAU;QACrCwW,UAAUxW;IACZ;IACAwW,UAAUH;IACV,IAAI9L,WAAW;QACbiM,UAAU;IACZ;IAEA,MAAMjE,UAAUiE,SAASrD,eAAeL,WAAW,GAAGwD;IAEtD,MAAMI,gBAAgB,MAAM3W,gBAAgBsB,UAAUkR,SAASrN,YAAY;QACzEqF;QACAqJ;IACF;IAEA,OAAO,IAAI/D,qBAAY,CAAC6G,eAAe;QAAEjR;IAAS;AACpD;AAYO,MAAM9F,eAA4B,CACvC+D,KACAuB,KACA5D,UACAC,OACA4D,YACAE,eACAC;IAEA,OAAOzF,iBACL8D,KACAuB,KACA5D,UACAC,OACA4D,YACAA,YACAE,eACAC;AAEJ"}