{"version": 3, "sources": ["../../../../src/server/normalizers/request/next-data.ts"], "sourcesContent": ["import type { PathnameNormalizer } from './pathname-normalizer'\n\nimport { denormalizePagePath } from '../../../shared/lib/page-path/denormalize-page-path'\nimport { PrefixPathnameNormalizer } from './prefix'\nimport { SuffixPathnameNormalizer } from './suffix'\n\nexport class NextDataPathnameNormalizer implements PathnameNormalizer {\n  private readonly prefix: PrefixPathnameNormalizer\n  private readonly suffix = new SuffixPathnameNormalizer('.json')\n  constructor(buildID: string) {\n    if (!buildID) {\n      throw new Error('Invariant: buildID is required')\n    }\n\n    this.prefix = new PrefixPathnameNormalizer(`/_next/data/${buildID}`)\n  }\n\n  public match(pathname: string) {\n    return this.prefix.match(pathname) && this.suffix.match(pathname)\n  }\n\n  public normalize(pathname: string, matched?: boolean): string {\n    // If we're not matched and we don't match, we don't need to normalize.\n    if (!matched && !this.match(pathname)) return pathname\n\n    pathname = this.prefix.normalize(pathname, true)\n    pathname = this.suffix.normalize(pathname, true)\n\n    return denormalizePagePath(pathname)\n  }\n}\n"], "names": ["NextDataPathnameNormalizer", "constructor", "buildID", "suffix", "SuffixPathnameNormalizer", "Error", "prefix", "PrefixPathnameNormalizer", "match", "pathname", "normalize", "matched", "denormalizePagePath"], "mappings": ";;;;+BAMaA;;;eAAAA;;;qCAJuB;wBACK;wBACA;AAElC,MAAMA;IAGXC,YAAYC,OAAe,CAAE;aADZC,SAAS,IAAIC,gCAAwB,CAAC;QAErD,IAAI,CAACF,SAAS;YACZ,MAAM,qBAA2C,CAA3C,IAAIG,MAAM,mCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0C;QAClD;QAEA,IAAI,CAACC,MAAM,GAAG,IAAIC,gCAAwB,CAAC,CAAC,YAAY,EAAEL,SAAS;IACrE;IAEOM,MAAMC,QAAgB,EAAE;QAC7B,OAAO,IAAI,CAACH,MAAM,CAACE,KAAK,CAACC,aAAa,IAAI,CAACN,MAAM,CAACK,KAAK,CAACC;IAC1D;IAEOC,UAAUD,QAAgB,EAAEE,OAAiB,EAAU;QAC5D,uEAAuE;QACvE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACH,KAAK,CAACC,WAAW,OAAOA;QAE9CA,WAAW,IAAI,CAACH,MAAM,CAACI,SAAS,CAACD,UAAU;QAC3CA,WAAW,IAAI,CAACN,MAAM,CAACO,SAAS,CAACD,UAAU;QAE3C,OAAOG,IAAAA,wCAAmB,EAACH;IAC7B;AACF"}