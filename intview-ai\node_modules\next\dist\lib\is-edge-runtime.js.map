{"version": 3, "sources": ["../../src/lib/is-edge-runtime.ts"], "sourcesContent": ["import type { ServerRuntime } from '../types'\nimport { SERVER_RUNTIME } from './constants'\n\nexport function isEdgeRuntime(value?: string): value is ServerRuntime {\n  return (\n    value === SERVER_RUNTIME.experimentalEdge || value === SERVER_RUNTIME.edge\n  )\n}\n"], "names": ["isEdgeRuntime", "value", "SERVER_RUNTIME", "experimentalEdge", "edge"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;2BAFe;AAExB,SAASA,cAAcC,KAAc;IAC1C,OACEA,UAAUC,yBAAc,CAACC,gBAAgB,IAAIF,UAAUC,yBAAc,CAACE,IAAI;AAE9E"}