{"version": 3, "sources": ["../../src/server/htmlescape.ts"], "sourcesContent": ["// This utility is based on https://github.com/zertosh/htmlescape\n// License: https://github.com/zertosh/htmlescape/blob/0527ca7156a524d256101bb310a9f970f63078ad/LICENSE\n\nconst ESCAPE_LOOKUP: { [match: string]: string } = {\n  '&': '\\\\u0026',\n  '>': '\\\\u003e',\n  '<': '\\\\u003c',\n  '\\u2028': '\\\\u2028',\n  '\\u2029': '\\\\u2029',\n}\n\nexport const ESCAPE_REGEX = /[&><\\u2028\\u2029]/g\n\nexport function htmlEscapeJsonString(str: string): string {\n  return str.replace(ESCAPE_REGEX, (match) => ESCAPE_LOOKUP[match])\n}\n"], "names": ["ESCAPE_LOOKUP", "ESCAPE_REGEX", "htmlEscapeJsonString", "str", "replace", "match"], "mappings": "AAAA,iEAAiE;AACjE,uGAAuG;AAEvG,MAAMA,gBAA6C;IACjD,KAAK;IACL,KAAK;IACL,KAAK;IACL,UAAU;IACV,UAAU;AACZ;AAEA,OAAO,MAAMC,eAAe,qBAAoB;AAEhD,OAAO,SAASC,qBAAqBC,GAAW;IAC9C,OAAOA,IAAIC,OAAO,CAACH,cAAc,CAACI,QAAUL,aAAa,CAACK,MAAM;AAClE"}