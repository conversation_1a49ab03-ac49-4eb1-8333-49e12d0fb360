(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/forms/SocialAuthForm.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// import { Button } from "../ui/button";
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
"use client";
;
;
// import { toast } from "sonner";
// import { signIn } from "next-auth/react";
// import ROUTES from "@/constants/routes";
const SocialAuthForm = ()=>{
    // const buttonClassName = `background-dark400_light900 body-medium text-dark200_light800
    // rounded-2  min-h-12 flex-1 px-4 py-3`;
    //   const handleSignIn = (provider: "github" | "google") => {
    //     console.log("Handle github");
    //   try {
    //     // signIn(provider, {
    //     //   redirectTo: ROUTES.HOME,
    //     // redirect: false,
    //     // });
    //   } catch (error) {
    //     console.log(error);
    //     toast.error(
    //       error instanceof Error
    //         ? error.message
    //         : "An error occoured during sign-in"
    //     );
    //   }
    // };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-wrap mt-10 gap-2.5",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mr-10",
                children: "Or Continue with "
            }, void 0, false, {
                fileName: "[project]/components/forms/SocialAuthForm.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                src: "/icons/facebook.svg",
                alt: "Facebook",
                width: 20,
                height: 20,
                className: "object-contain mr-2.5 "
            }, void 0, false, {
                fileName: "[project]/components/forms/SocialAuthForm.tsx",
                lineNumber: 38,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                src: "/icons/google.svg",
                alt: "Google",
                width: 20,
                height: 20,
                className: "object-contain mr-2.5"
            }, void 0, false, {
                fileName: "[project]/components/forms/SocialAuthForm.tsx",
                lineNumber: 50,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                src: "/icons/apple.svg",
                alt: "Apple",
                width: 20,
                height: 20,
                className: "object-contain mr-2.5 "
            }, void 0, false, {
                fileName: "[project]/components/forms/SocialAuthForm.tsx",
                lineNumber: 59,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/forms/SocialAuthForm.tsx",
        lineNumber: 32,
        columnNumber: 5
    }, this);
};
_c = SocialAuthForm;
const __TURBOPACK__default__export__ = SocialAuthForm;
var _c;
__turbopack_context__.k.register(_c, "SocialAuthForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=components_forms_SocialAuthForm_tsx_8c888aca._.js.map