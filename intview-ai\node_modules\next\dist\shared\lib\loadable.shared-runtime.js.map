{"version": 3, "sources": ["../../../src/shared/lib/loadable.shared-runtime.tsx"], "sourcesContent": ["// TODO: Remove use of `any` type.\n/**\n@copyright (c) 2017-present <PERSON> <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE\n*/\n// https://github.com/jamiebuilds/react-loadable/blob/v5.5.0/src/index.js\n// Modified to be compatible with webpack 4 / Next.js\n\nimport React from 'react'\nimport { LoadableContext } from './loadable-context.shared-runtime'\n\nfunction resolve(obj: any) {\n  return obj && obj.default ? obj.default : obj\n}\n\nconst ALL_INITIALIZERS: any[] = []\nconst READY_INITIALIZERS: any[] = []\nlet initialized = false\n\nfunction load(loader: any) {\n  let promise = loader()\n\n  let state: any = {\n    loading: true,\n    loaded: null,\n    error: null,\n  }\n\n  state.promise = promise\n    .then((loaded: any) => {\n      state.loading = false\n      state.loaded = loaded\n      return loaded\n    })\n    .catch((err: any) => {\n      state.loading = false\n      state.error = err\n      throw err\n    })\n\n  return state\n}\n\nfunction createLoadableComponent(loadFn: any, options: any) {\n  let opts = Object.assign(\n    {\n      loader: null,\n      loading: null,\n      delay: 200,\n      timeout: null,\n      webpack: null,\n      modules: null,\n    },\n    options\n  )\n\n  /** @type LoadableSubscription */\n  let subscription: any = null\n  function init() {\n    if (!subscription) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      const sub = new LoadableSubscription(loadFn, opts)\n      subscription = {\n        getCurrentValue: sub.getCurrentValue.bind(sub),\n        subscribe: sub.subscribe.bind(sub),\n        retry: sub.retry.bind(sub),\n        promise: sub.promise.bind(sub),\n      }\n    }\n    return subscription.promise()\n  }\n\n  // Server only\n  if (typeof window === 'undefined') {\n    ALL_INITIALIZERS.push(init)\n  }\n\n  // Client only\n  if (!initialized && typeof window !== 'undefined') {\n    // require.resolveWeak check is needed for environments that don't have it available like Jest\n    const moduleIds =\n      opts.webpack && typeof (require as any).resolveWeak === 'function'\n        ? opts.webpack()\n        : opts.modules\n    if (moduleIds) {\n      READY_INITIALIZERS.push((ids: any) => {\n        for (const moduleId of moduleIds) {\n          if (ids.includes(moduleId)) {\n            return init()\n          }\n        }\n      })\n    }\n  }\n\n  function useLoadableModule() {\n    init()\n\n    const context = React.useContext(LoadableContext)\n    if (context && Array.isArray(opts.modules)) {\n      opts.modules.forEach((moduleName: any) => {\n        context(moduleName)\n      })\n    }\n  }\n\n  function LoadableComponent(props: any, ref: any) {\n    useLoadableModule()\n\n    const state = (React as any).useSyncExternalStore(\n      subscription.subscribe,\n      subscription.getCurrentValue,\n      subscription.getCurrentValue\n    )\n\n    React.useImperativeHandle(\n      ref,\n      () => ({\n        retry: subscription.retry,\n      }),\n      []\n    )\n\n    return React.useMemo(() => {\n      if (state.loading || state.error) {\n        return React.createElement(opts.loading, {\n          isLoading: state.loading,\n          pastDelay: state.pastDelay,\n          timedOut: state.timedOut,\n          error: state.error,\n          retry: subscription.retry,\n        })\n      } else if (state.loaded) {\n        return React.createElement(resolve(state.loaded), props)\n      } else {\n        return null\n      }\n    }, [props, state])\n  }\n\n  LoadableComponent.preload = () => init()\n  LoadableComponent.displayName = 'LoadableComponent'\n\n  return React.forwardRef(LoadableComponent)\n}\n\nclass LoadableSubscription {\n  _loadFn: any\n  _opts: any\n  _callbacks: any\n  _delay: any\n  _timeout: any\n  _res: any\n  _state: any\n  constructor(loadFn: any, opts: any) {\n    this._loadFn = loadFn\n    this._opts = opts\n    this._callbacks = new Set()\n    this._delay = null\n    this._timeout = null\n\n    this.retry()\n  }\n\n  promise() {\n    return this._res.promise\n  }\n\n  retry() {\n    this._clearTimeouts()\n    this._res = this._loadFn(this._opts.loader)\n\n    this._state = {\n      pastDelay: false,\n      timedOut: false,\n    }\n\n    const { _res: res, _opts: opts } = this\n\n    if (res.loading) {\n      if (typeof opts.delay === 'number') {\n        if (opts.delay === 0) {\n          this._state.pastDelay = true\n        } else {\n          this._delay = setTimeout(() => {\n            this._update({\n              pastDelay: true,\n            })\n          }, opts.delay)\n        }\n      }\n\n      if (typeof opts.timeout === 'number') {\n        this._timeout = setTimeout(() => {\n          this._update({ timedOut: true })\n        }, opts.timeout)\n      }\n    }\n\n    this._res.promise\n      .then(() => {\n        this._update({})\n        this._clearTimeouts()\n      })\n      .catch((_err: any) => {\n        this._update({})\n        this._clearTimeouts()\n      })\n    this._update({})\n  }\n\n  _update(partial: any) {\n    this._state = {\n      ...this._state,\n      error: this._res.error,\n      loaded: this._res.loaded,\n      loading: this._res.loading,\n      ...partial,\n    }\n    this._callbacks.forEach((callback: any) => callback())\n  }\n\n  _clearTimeouts() {\n    clearTimeout(this._delay)\n    clearTimeout(this._timeout)\n  }\n\n  getCurrentValue() {\n    return this._state\n  }\n\n  subscribe(callback: any) {\n    this._callbacks.add(callback)\n    return () => {\n      this._callbacks.delete(callback)\n    }\n  }\n}\n\nfunction Loadable(opts: any) {\n  return createLoadableComponent(load, opts)\n}\n\nfunction flushInitializers(initializers: any, ids?: any): any {\n  let promises = []\n\n  while (initializers.length) {\n    let init = initializers.pop()\n    promises.push(init(ids))\n  }\n\n  return Promise.all(promises).then(() => {\n    if (initializers.length) {\n      return flushInitializers(initializers, ids)\n    }\n  })\n}\n\nLoadable.preloadAll = () => {\n  return new Promise((resolveInitializers, reject) => {\n    flushInitializers(ALL_INITIALIZERS).then(resolveInitializers, reject)\n  })\n}\n\nLoadable.preloadReady = (ids: (string | number)[] = []): Promise<void> => {\n  return new Promise<void>((resolvePreload) => {\n    const res = () => {\n      initialized = true\n      return resolvePreload()\n    }\n    // We always will resolve, errors should be handled within loading UIs.\n    flushInitializers(READY_INITIALIZERS, ids).then(res, res)\n  })\n}\n\ndeclare global {\n  interface Window {\n    __NEXT_PRELOADREADY?: (ids?: (string | number)[]) => Promise<void>\n  }\n}\n\nif (typeof window !== 'undefined') {\n  window.__NEXT_PRELOADREADY = Loadable.preloadReady\n}\n\nexport default Loadable\n"], "names": ["resolve", "obj", "default", "ALL_INITIALIZERS", "READY_INITIALIZERS", "initialized", "load", "loader", "promise", "state", "loading", "loaded", "error", "then", "catch", "err", "createLoadableComponent", "loadFn", "options", "opts", "Object", "assign", "delay", "timeout", "webpack", "modules", "subscription", "init", "sub", "LoadableSubscription", "getCurrentValue", "bind", "subscribe", "retry", "window", "push", "moduleIds", "require", "resolveWeak", "ids", "moduleId", "includes", "useLoadableModule", "context", "React", "useContext", "LoadableContext", "Array", "isArray", "for<PERSON>ach", "moduleName", "LoadableComponent", "props", "ref", "useSyncExternalStore", "useImperativeHandle", "useMemo", "createElement", "isLoading", "past<PERSON>elay", "timedOut", "preload", "displayName", "forwardRef", "_res", "_clearTimeouts", "_loadFn", "_opts", "_state", "res", "_delay", "setTimeout", "_update", "_timeout", "_err", "partial", "_callbacks", "callback", "clearTimeout", "add", "delete", "constructor", "Set", "Loadable", "flushInitializers", "initializers", "promises", "length", "pop", "Promise", "all", "preloadAll", "resolveInitializers", "reject", "preloadReady", "resolvePreload", "__NEXT_PRELOADREADY"], "mappings": "AAAA,kCAAkC;AAClC;;;;;;;;;;;;;;;;;;;AAmBA,GACA,yEAAyE;AACzE,qDAAqD;;;;;+BAuRrD;;;eAAA;;;;gEArRkB;8CACc;AAEhC,SAASA,QAAQC,GAAQ;IACvB,OAAOA,OAAOA,IAAIC,OAAO,GAAGD,IAAIC,OAAO,GAAGD;AAC5C;AAEA,MAAME,mBAA0B,EAAE;AAClC,MAAMC,qBAA4B,EAAE;AACpC,IAAIC,cAAc;AAElB,SAASC,KAAKC,MAAW;IACvB,IAAIC,UAAUD;IAEd,IAAIE,QAAa;QACfC,SAAS;QACTC,QAAQ;QACRC,OAAO;IACT;IAEAH,MAAMD,OAAO,GAAGA,QACbK,IAAI,CAAC,CAACF;QACLF,MAAMC,OAAO,GAAG;QAChBD,MAAME,MAAM,GAAGA;QACf,OAAOA;IACT,GACCG,KAAK,CAAC,CAACC;QACNN,MAAMC,OAAO,GAAG;QAChBD,MAAMG,KAAK,GAAGG;QACd,MAAMA;IACR;IAEF,OAAON;AACT;AAEA,SAASO,wBAAwBC,MAAW,EAAEC,OAAY;IACxD,IAAIC,OAAOC,OAAOC,MAAM,CACtB;QACEd,QAAQ;QACRG,SAAS;QACTY,OAAO;QACPC,SAAS;QACTC,SAAS;QACTC,SAAS;IACX,GACAP;IAGF,+BAA+B,GAC/B,IAAIQ,eAAoB;IACxB,SAASC;QACP,IAAI,CAACD,cAAc;YACjB,mEAAmE;YACnE,MAAME,MAAM,IAAIC,qBAAqBZ,QAAQE;YAC7CO,eAAe;gBACbI,iBAAiBF,IAAIE,eAAe,CAACC,IAAI,CAACH;gBAC1CI,WAAWJ,IAAII,SAAS,CAACD,IAAI,CAACH;gBAC9BK,OAAOL,IAAIK,KAAK,CAACF,IAAI,CAACH;gBACtBpB,SAASoB,IAAIpB,OAAO,CAACuB,IAAI,CAACH;YAC5B;QACF;QACA,OAAOF,aAAalB,OAAO;IAC7B;IAEA,cAAc;IACd,IAAI,OAAO0B,WAAW,aAAa;QACjC/B,iBAAiBgC,IAAI,CAACR;IACxB;IAEA,cAAc;IACd,IAAI,CAACtB,eAAe,OAAO6B,WAAW,aAAa;QACjD,8FAA8F;QAC9F,MAAME,YACJjB,KAAKK,OAAO,IAAI,OAAO,AAACa,QAAgBC,WAAW,KAAK,aACpDnB,KAAKK,OAAO,KACZL,KAAKM,OAAO;QAClB,IAAIW,WAAW;YACbhC,mBAAmB+B,IAAI,CAAC,CAACI;gBACvB,KAAK,MAAMC,YAAYJ,UAAW;oBAChC,IAAIG,IAAIE,QAAQ,CAACD,WAAW;wBAC1B,OAAOb;oBACT;gBACF;YACF;QACF;IACF;IAEA,SAASe;QACPf;QAEA,MAAMgB,UAAUC,cAAK,CAACC,UAAU,CAACC,6CAAe;QAChD,IAAIH,WAAWI,MAAMC,OAAO,CAAC7B,KAAKM,OAAO,GAAG;YAC1CN,KAAKM,OAAO,CAACwB,OAAO,CAAC,CAACC;gBACpBP,QAAQO;YACV;QACF;IACF;IAEA,SAASC,kBAAkBC,KAAU,EAAEC,GAAQ;QAC7CX;QAEA,MAAMjC,QAAQ,AAACmC,cAAK,CAASU,oBAAoB,CAC/C5B,aAAaM,SAAS,EACtBN,aAAaI,eAAe,EAC5BJ,aAAaI,eAAe;QAG9Bc,cAAK,CAACW,mBAAmB,CACvBF,KACA,IAAO,CAAA;gBACLpB,OAAOP,aAAaO,KAAK;YAC3B,CAAA,GACA,EAAE;QAGJ,OAAOW,cAAK,CAACY,OAAO,CAAC;YACnB,IAAI/C,MAAMC,OAAO,IAAID,MAAMG,KAAK,EAAE;gBAChC,qBAAOgC,cAAK,CAACa,aAAa,CAACtC,KAAKT,OAAO,EAAE;oBACvCgD,WAAWjD,MAAMC,OAAO;oBACxBiD,WAAWlD,MAAMkD,SAAS;oBAC1BC,UAAUnD,MAAMmD,QAAQ;oBACxBhD,OAAOH,MAAMG,KAAK;oBAClBqB,OAAOP,aAAaO,KAAK;gBAC3B;YACF,OAAO,IAAIxB,MAAME,MAAM,EAAE;gBACvB,qBAAOiC,cAAK,CAACa,aAAa,CAACzD,QAAQS,MAAME,MAAM,GAAGyC;YACpD,OAAO;gBACL,OAAO;YACT;QACF,GAAG;YAACA;YAAO3C;SAAM;IACnB;IAEA0C,kBAAkBU,OAAO,GAAG,IAAMlC;IAClCwB,kBAAkBW,WAAW,GAAG;IAEhC,qBAAOlB,cAAK,CAACmB,UAAU,CAACZ;AAC1B;AAEA,MAAMtB;IAkBJrB,UAAU;QACR,OAAO,IAAI,CAACwD,IAAI,CAACxD,OAAO;IAC1B;IAEAyB,QAAQ;QACN,IAAI,CAACgC,cAAc;QACnB,IAAI,CAACD,IAAI,GAAG,IAAI,CAACE,OAAO,CAAC,IAAI,CAACC,KAAK,CAAC5D,MAAM;QAE1C,IAAI,CAAC6D,MAAM,GAAG;YACZT,WAAW;YACXC,UAAU;QACZ;QAEA,MAAM,EAAEI,MAAMK,GAAG,EAAEF,OAAOhD,IAAI,EAAE,GAAG,IAAI;QAEvC,IAAIkD,IAAI3D,OAAO,EAAE;YACf,IAAI,OAAOS,KAAKG,KAAK,KAAK,UAAU;gBAClC,IAAIH,KAAKG,KAAK,KAAK,GAAG;oBACpB,IAAI,CAAC8C,MAAM,CAACT,SAAS,GAAG;gBAC1B,OAAO;oBACL,IAAI,CAACW,MAAM,GAAGC,WAAW;wBACvB,IAAI,CAACC,OAAO,CAAC;4BACXb,WAAW;wBACb;oBACF,GAAGxC,KAAKG,KAAK;gBACf;YACF;YAEA,IAAI,OAAOH,KAAKI,OAAO,KAAK,UAAU;gBACpC,IAAI,CAACkD,QAAQ,GAAGF,WAAW;oBACzB,IAAI,CAACC,OAAO,CAAC;wBAAEZ,UAAU;oBAAK;gBAChC,GAAGzC,KAAKI,OAAO;YACjB;QACF;QAEA,IAAI,CAACyC,IAAI,CAACxD,OAAO,CACdK,IAAI,CAAC;YACJ,IAAI,CAAC2D,OAAO,CAAC,CAAC;YACd,IAAI,CAACP,cAAc;QACrB,GACCnD,KAAK,CAAC,CAAC4D;YACN,IAAI,CAACF,OAAO,CAAC,CAAC;YACd,IAAI,CAACP,cAAc;QACrB;QACF,IAAI,CAACO,OAAO,CAAC,CAAC;IAChB;IAEAA,QAAQG,OAAY,EAAE;QACpB,IAAI,CAACP,MAAM,GAAG;YACZ,GAAG,IAAI,CAACA,MAAM;YACdxD,OAAO,IAAI,CAACoD,IAAI,CAACpD,KAAK;YACtBD,QAAQ,IAAI,CAACqD,IAAI,CAACrD,MAAM;YACxBD,SAAS,IAAI,CAACsD,IAAI,CAACtD,OAAO;YAC1B,GAAGiE,OAAO;QACZ;QACA,IAAI,CAACC,UAAU,CAAC3B,OAAO,CAAC,CAAC4B,WAAkBA;IAC7C;IAEAZ,iBAAiB;QACfa,aAAa,IAAI,CAACR,MAAM;QACxBQ,aAAa,IAAI,CAACL,QAAQ;IAC5B;IAEA3C,kBAAkB;QAChB,OAAO,IAAI,CAACsC,MAAM;IACpB;IAEApC,UAAU6C,QAAa,EAAE;QACvB,IAAI,CAACD,UAAU,CAACG,GAAG,CAACF;QACpB,OAAO;YACL,IAAI,CAACD,UAAU,CAACI,MAAM,CAACH;QACzB;IACF;IAlFAI,YAAYhE,MAAW,EAAEE,IAAS,CAAE;QAClC,IAAI,CAAC+C,OAAO,GAAGjD;QACf,IAAI,CAACkD,KAAK,GAAGhD;QACb,IAAI,CAACyD,UAAU,GAAG,IAAIM;QACtB,IAAI,CAACZ,MAAM,GAAG;QACd,IAAI,CAACG,QAAQ,GAAG;QAEhB,IAAI,CAACxC,KAAK;IACZ;AA2EF;AAEA,SAASkD,SAAShE,IAAS;IACzB,OAAOH,wBAAwBV,MAAMa;AACvC;AAEA,SAASiE,kBAAkBC,YAAiB,EAAE9C,GAAS;IACrD,IAAI+C,WAAW,EAAE;IAEjB,MAAOD,aAAaE,MAAM,CAAE;QAC1B,IAAI5D,OAAO0D,aAAaG,GAAG;QAC3BF,SAASnD,IAAI,CAACR,KAAKY;IACrB;IAEA,OAAOkD,QAAQC,GAAG,CAACJ,UAAUzE,IAAI,CAAC;QAChC,IAAIwE,aAAaE,MAAM,EAAE;YACvB,OAAOH,kBAAkBC,cAAc9C;QACzC;IACF;AACF;AAEA4C,SAASQ,UAAU,GAAG;IACpB,OAAO,IAAIF,QAAQ,CAACG,qBAAqBC;QACvCT,kBAAkBjF,kBAAkBU,IAAI,CAAC+E,qBAAqBC;IAChE;AACF;AAEAV,SAASW,YAAY,GAAG,CAACvD;QAAAA,gBAAAA,MAA2B,EAAE;IACpD,OAAO,IAAIkD,QAAc,CAACM;QACxB,MAAM1B,MAAM;YACVhE,cAAc;YACd,OAAO0F;QACT;QACA,uEAAuE;QACvEX,kBAAkBhF,oBAAoBmC,KAAK1B,IAAI,CAACwD,KAAKA;IACvD;AACF;AAQA,IAAI,OAAOnC,WAAW,aAAa;IACjCA,OAAO8D,mBAAmB,GAAGb,SAASW,YAAY;AACpD;MAEA,WAAeX"}