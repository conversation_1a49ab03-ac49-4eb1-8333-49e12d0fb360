{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewInstructions.tsx"], "sourcesContent": ["\"use client\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport React, { useState } from \"react\";\r\n\r\ntype InterviewInstructionsProps = {\r\n  candidateName?: string;\r\n  jobTitle?: string;\r\n  languages?: string[];\r\n  instructions?: string[];\r\n  environmentChecklist?: string[];\r\n  disclaimers?: string[];\r\n  onNext?: () => void;\r\n};\r\n\r\nconst defaultInstructions = [\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n];\r\n\r\nconst defaultEnvironment = [\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n];\r\n\r\nconst defaultDisclaimers = [\r\n  \"Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .\",\r\n  \"AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .\",\r\n  \"Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .\",\r\n  \"Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer .\",\r\n];\r\n\r\nconst InterviewInstructions: React.FC<InterviewInstructionsProps> = ({\r\n  candidateName = \"Jonathan\",\r\n  jobTitle = \"Insurance Agent\",\r\n  languages = [\"English\", \"Chinese\"],\r\n  instructions = defaultInstructions,\r\n  environmentChecklist = defaultEnvironment,\r\n  disclaimers = defaultDisclaimers,\r\n  onNext,\r\n}) => {\r\n  const [isChecked, setIsChecked] = useState(false);\r\n\r\n  return (\r\n    <div className=\"flex-1 border border-gray-400 rounded-md h-fit bg-white\">\r\n      <div className=\"p-4 flex flex-col text-[#38383a]\">\r\n        <p className=\"font-semibold mb-8 text-xl\">\r\n          Instructions for Interview!\r\n        </p>\r\n        <div className=\"space-y-6\">\r\n          <div>\r\n            <p className=\" mb-2 text-md\">Hello {candidateName}!</p>\r\n            <p className=\"text-sm mb-4\">\r\n              As part of the process you are required to complete an AI video\r\n              assessment for the role of the {jobTitle}.\r\n            </p>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Interview Language</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {languages.map((language, index) => (\r\n                <li key={index}>{language}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Instructions</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {instructions.map((instruction, index) => (\r\n                <li key={index}>{instruction}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Environment Checklist:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {environmentChecklist.map((item, index) => (\r\n                <li key={index}>{item}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Important Disclaimers:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {disclaimers.map((disclaimer, index) => (\r\n                <li key={index}>{disclaimer}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"flex items-start gap-2 mt-6\">\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"terms\"\r\n              checked={isChecked}\r\n              onChange={(e) => setIsChecked(e.target.checked)}\r\n              className=\"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\r\n            />\r\n            <label htmlFor=\"terms\" className=\"text-[11px] text-[#38383a]\">\r\n              By checking this box, you agree with AI Interview{\" \"}\r\n              <span className=\"text-primary cursor-pointer font-medium\">\r\n                Terms of use\r\n              </span>\r\n              .\r\n            </label>\r\n          </div>\r\n          <div className=\"flex justify-center\">\r\n            <Button\r\n              disabled={!isChecked}\r\n              variant=\"default\"\r\n              size=\"lg\"\r\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n              onClick={() => onNext && onNext()}\r\n            >\r\n            Proceed\r\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewInstructions;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAeA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,wBAA8D,CAAC,EACnE,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,YAAY;IAAC;IAAW;CAAU,EAClC,eAAe,mBAAmB,EAClC,uBAAuB,kBAAkB,EACzC,cAAc,kBAAkB,EAChC,MAAM,EACP;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;;wCAAgB;wCAAO;wCAAc;;;;;;;8CAClD,8OAAC;oCAAE,WAAU;;wCAAe;wCAEM;wCAAS;;;;;;;;;;;;;sCAI7C,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,SAAS;oCACT,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,OAAO;oCAC9C,WAAU;;;;;;8CAEZ,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAA6B;wCACV;sDAClD,8OAAC;4CAAK,WAAU;sDAA0C;;;;;;wCAEnD;;;;;;;;;;;;;sCAIX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;gCACL,UAAU,CAAC;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;oCAC1B;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;uCAEe", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/JobInfoCard.tsx"], "sourcesContent": ["import { MapPin, BriefcaseBusiness } from \"lucide-react\";\r\n\r\nconst JobInfoCard = () => {\r\n  return (\r\n    <div className=\"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl\">\r\n      <div className=\"flex justify-between items-start\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold mb-3\">\r\n            UX/UI Designer for Ai-Interview Web App\r\n          </h2>\r\n          <div className=\"flex gap-2 leading-relaxed mb-3 flex-wrap\">\r\n            <p className=\"text-sm text-gray-600 font-medium\">\r\n              $500 - $1000 <span className=\"font-extrabold px-1\">·</span>\r\n            </p>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <MapPin className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">New York</p>\r\n            </div>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <BriefcaseBusiness className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">\r\n                Onsite / Remote\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            We&apos;re building an AI-powered interview tool. We expect you to\r\n            help users prepare by giving human interview experience generation.\r\n          </p>\r\n        </div>\r\n        <span className=\"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium\">\r\n          Active\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobInfoCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAG3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAoC;sDAClC,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAKrD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC;oBAAK,WAAU;8BAAyE;;;;;;;;;;;;;;;;;AAMjG;uCAEe", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/context/InterviewContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { createContext, useContext, useState, useCallback, ReactNode } from \"react\";\r\n\r\nconst DID_API_URL = \"https://api.d-id.com\";\r\n\r\ninterface Agent {\r\n  id: string;\r\n  preview_name: string;\r\n  status: string;\r\n  presenter: {\r\n    type: string;\r\n    voice: {\r\n      type: string;\r\n      voice_id: string;\r\n    };\r\n    thumbnail: string;\r\n    source_url: string;\r\n  };\r\n  llm: {\r\n    type: string;\r\n    provider: string;\r\n    model: string;\r\n    instructions: string;\r\n  };\r\n}\r\n\r\ninterface InterviewContextType {\r\n  // D-ID Agent state\r\n  agent: Agent | null;\r\n  isCreatingAgent: boolean;\r\n  agentError: string | null;\r\n  createAgent: (instructions: string, agentName: string) => Promise<void>;\r\n  \r\n  // Interview state\r\n  currentQuestion: number;\r\n  setCurrentQuestion: (question: number) => void;\r\n  isInterviewStarted: boolean;\r\n  setIsInterviewStarted: (started: boolean) => void;\r\n  \r\n  // Questions data\r\n  questions: string[];\r\n}\r\n\r\nconst InterviewContext = createContext<InterviewContextType | undefined>(undefined);\r\n\r\ninterface InterviewProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const InterviewProvider: React.FC<InterviewProviderProps> = ({ children }) => {\r\n  // D-ID Agent state\r\n  const [agent, setAgent] = useState<Agent | null>(null);\r\n  const [isCreatingAgent, setIsCreatingAgent] = useState<boolean>(false);\r\n  const [agentError, setAgentError] = useState<string | null>(null);\r\n  \r\n  // Interview state\r\n  const [currentQuestion, setCurrentQuestion] = useState<number>(1);\r\n  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);\r\n  \r\n  // Questions data\r\n  const questions = [\r\n    \"Tell us about yourself?\",\r\n    \"What are your strengths?\",\r\n    \"Why do you want this job?\",\r\n    \"Where do you see yourself in 5 years?\",\r\n  ];\r\n\r\n  const getAuthHeaders = () => {\r\n    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || \"\";\r\n    console.log(\"Using D-ID API Key:\", apiKey ? `${apiKey.substring(0, 10)}...` : \"NOT_FOUND\");\r\n\r\n    return {\r\n      \"Authorization\": `Basic ${apiKey}`,\r\n      \"Content-Type\": \"application/json\",\r\n    };\r\n  };\r\n\r\n  const createAgent = useCallback(async (instructions: string, agentName: string) => {\r\n    // If agent already exists with same instructions, don't recreate\r\n    if (agent && agent.llm.instructions === instructions && agent.preview_name === agentName) {\r\n      return;\r\n    }\r\n\r\n    setIsCreatingAgent(true);\r\n    setAgentError(null);\r\n\r\n    const payload = {\r\n      presenter: {\r\n        type: \"talk\",\r\n        voice: {\r\n          type: \"microsoft\",\r\n          voice_id: \"en-US-JennyMultilingualV2Neural\"\r\n        },\r\n        thumbnail: \"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg\",\r\n        source_url: \"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg\"\r\n      },\r\n      llm: {\r\n        type: \"openai\",\r\n        provider: \"openai\",\r\n        model: \"gpt-4o-mini\",\r\n        instructions: instructions\r\n      },\r\n      preview_name: agentName\r\n    };\r\n\r\n    try {\r\n      console.log(\"Creating D-ID Agent with payload:\", payload);\r\n\r\n      const response = await fetch(`${DID_API_URL}/agents`, {\r\n        method: \"POST\",\r\n        headers: getAuthHeaders(),\r\n        body: JSON.stringify(payload),\r\n      });\r\n\r\n      console.log(\"D-ID Agent API Response Status:\", response.status);\r\n\r\n      if (!response.ok) {\r\n        const errorText = await response.text();\r\n        console.error(\"D-ID Agent API Error Response:\", errorText);\r\n        throw new Error(`Failed to create agent: ${response.status} ${response.statusText} - ${errorText}`);\r\n      }\r\n\r\n      const agentData: Agent = await response.json();\r\n      console.log(\"D-ID Agent Created Successfully:\", agentData);\r\n      setAgent(agentData);\r\n    } catch (err: unknown) {\r\n      console.error(\"D-ID Agent Creation Error:\", err);\r\n      const errorMessage = err instanceof Error ? err.message : \"Failed to create agent\";\r\n      setAgentError(`Agent Creation Failed: ${errorMessage}`);\r\n    } finally {\r\n      setIsCreatingAgent(false);\r\n    }\r\n  }, [agent]);\r\n\r\n  const value: InterviewContextType = {\r\n    // D-ID Agent state\r\n    agent,\r\n    isCreatingAgent,\r\n    agentError,\r\n    createAgent,\r\n    \r\n    // Interview state\r\n    currentQuestion,\r\n    setCurrentQuestion,\r\n    isInterviewStarted,\r\n    setIsInterviewStarted,\r\n    \r\n    // Questions data\r\n    questions,\r\n  };\r\n\r\n  return (\r\n    <InterviewContext.Provider value={value}>\r\n      {children}\r\n    </InterviewContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useInterview = (): InterviewContextType => {\r\n  const context = useContext(InterviewContext);\r\n  if (context === undefined) {\r\n    throw new Error('useInterview must be used within an InterviewProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAGA,MAAM,cAAc;AAwCpB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAMlE,MAAM,oBAAsD,CAAC,EAAE,QAAQ,EAAE;IAC9E,mBAAmB;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,kBAAkB;IAClB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEtE,iBAAiB;IACjB,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB,MAAM,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI,QAAQ,GAAG,CAAC,WAAW,IAAI;QACjF,QAAQ,GAAG,CAAC,uBAAuB,SAAS,GAAG,OAAO,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;QAE9E,OAAO;YACL,iBAAiB,CAAC,MAAM,EAAE,QAAQ;YAClC,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,cAAsB;QAC3D,iEAAiE;QACjE,IAAI,SAAS,MAAM,GAAG,CAAC,YAAY,KAAK,gBAAgB,MAAM,YAAY,KAAK,WAAW;YACxF;QACF;QAEA,mBAAmB;QACnB,cAAc;QAEd,MAAM,UAAU;YACd,WAAW;gBACT,MAAM;gBACN,OAAO;oBACL,MAAM;oBACN,UAAU;gBACZ;gBACA,WAAW;gBACX,YAAY;YACd;YACA,KAAK;gBACH,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,cAAc;YAChB;YACA,cAAc;QAChB;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,OAAO,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,mCAAmC,SAAS,MAAM;YAE9D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YACpG;YAEA,MAAM,YAAmB,MAAM,SAAS,IAAI;YAC5C,QAAQ,GAAG,CAAC,oCAAoC;YAChD,SAAS;QACX,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,cAAc,CAAC,uBAAuB,EAAE,cAAc;QACxD,SAAU;YACR,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,QAA8B;QAClC,mBAAmB;QACnB;QACA;QACA;QACA;QAEA,kBAAkB;QAClB;QACA;QACA;QACA;QAEA,iBAAiB;QACjB;IACF;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;AAEO,MAAM,eAAe;IAC1B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/QuestionsList.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useInterview } from \"@/context/InterviewContext\";\r\n\r\ntype QuestionsListProps = {\r\n  className?: string;\r\n};\r\n\r\nconst QuestionsList = ({ className }: QuestionsListProps) => {\r\n  const { questions, currentQuestion } = useInterview();\r\n\r\n  return (\r\n    <div\r\n      className={`rounded-2xl bg-white p-4 w-full  shadow-sm overflow-y-auto scrollbar-hidden ${\r\n        className || \"\"\r\n      }`}\r\n    >\r\n      {\" \"}\r\n      <h3 className=\"font-semibold text-lg mb-6\">Questions</h3>\r\n      <ul className=\"relative space-y-8  \">\r\n        {Array.from({ length: 4 }, (_, i) => (\r\n          <li\r\n            key={i}\r\n            className=\"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5\"\r\n          >\r\n            {i !== 3 && (\r\n              <span className=\"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]\" />\r\n            )}\r\n            <div\r\n              className={`rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"bg-[#6938EF] text-white\"\r\n                  : i + 1 < currentQuestion\r\n                    ? \"bg-green-500 text-white\"\r\n                    : \"bg-[#C7ACF5] text-white\"\r\n              }`}\r\n            >\r\n              {i + 1 < currentQuestion ? \"✓\" : i + 1}\r\n            </div>\r\n            <span\r\n              className={`text-md font-medium mt-7 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"text-[#6938EF] font-semibold\"\r\n                  : \"text-[#616161]\"\r\n              }`}\r\n            >\r\n              {questions[i]}\r\n            </span>\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsList;\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAOA,MAAM,gBAAgB,CAAC,EAAE,SAAS,EAAsB;IACtD,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAElD,qBACE,8OAAC;QACC,WAAW,CAAC,4EAA4E,EACtF,aAAa,IACb;;YAED;0BACD,8OAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,8OAAC;gBAAG,WAAU;0BACX,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC;wBAEC,WAAU;;4BAET,MAAM,mBACL,8OAAC;gCAAK,WAAU;;;;;;0CAElB,8OAAC;gCACC,WAAW,CAAC,wFAAwF,EAClG,IAAI,MAAM,kBACN,4BACA,IAAI,IAAI,kBACN,4BACA,2BACN;0CAED,IAAI,IAAI,kBAAkB,MAAM,IAAI;;;;;;0CAEvC,8OAAC;gCACC,WAAW,CAAC,yBAAyB,EACnC,IAAI,MAAM,kBACN,iCACA,kBACJ;0CAED,SAAS,CAAC,EAAE;;;;;;;uBAxBV;;;;;;;;;;;;;;;;AA+BjB;uCAEe", "debugId": null}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewLayout.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\nconst InterviewLayout = ({ children }: { children: ReactNode }) => {\r\n  return (\r\n    <div className=\"border rounded-lg p-6 min-h-[600px] mb-4 flex-1\">\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewLayout;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAA2B;IAC5D,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewRecording.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from \"lucide-react\";\r\nimport JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport CandidateWithAgent from \"../CandidateWithAgent\";\r\n\r\ntype InterviewRecordingProps = {\r\n  onNext?: () => void;\r\n};\r\n\r\nconst InterviewRecording = ({ onNext }: InterviewRecordingProps) => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <JobInfoCard />\r\n\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start\">\r\n          <QuestionsList className=\"h-[550px]\" />\r\n          {/* <CandidateWithAgent\r\n            className=\"h-[550px]\"\r\n            useAgent={true}\r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n          /> */}\r\n        </div>\r\n\r\n        <div className=\"flex justify-center mt-10 gap-4\">\r\n          <Button\r\n            // disabled\r\n            variant=\"default\"\r\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n            onClick={() => onNext && onNext()}\r\n          >\r\n            Start Interview\r\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n          </Button>\r\n        </div>\r\n        <div className=\"flex justify-center mt-5 text-2xl font-semibold text-primary\">\r\n          02:00\r\n        </div>\r\n      </InterviewLayout>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewRecording;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAOA,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAA2B;IAC7D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,UAAW;;;;;0BAEZ,8OAAC,8HAAA,CAAA,UAAe;;kCACd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4HAAA,CAAA,UAAa;4BAAC,WAAU;;;;;;;;;;;kCAS3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,WAAW;4BACX,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG1B,8OAAC;wBAAI,WAAU;kCAA+D;;;;;;;;;;;;;;;;;;AAMtF;uCAEe", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28root%29/interview/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport InterviewInstructions from \"@/components/interview/InterviewInstructions\";\r\nimport QuestionsPage from \"@/components/interview/QuestionsPage\";\r\nimport FinishInterview from \"@/components/interview/FinishInterview\";\r\nimport Analysis from \"@/components/interview/Analysis\";\r\nimport InterviewRecording from \"../../../components/interview/InterviewRecording\";\r\nimport { InterviewProvider } from \"@/context/InterviewContext\";\r\n\r\ntype InterviewStep =\r\n  | \"instructions\"\r\n  | \"questions\"\r\n  | \"recording\"\r\n  | \"finishInterview\"\r\n  | \"analysis\";\r\n\r\nconst Interview = () => {\r\n  const [currentStep, setCurrentStep] = useState<InterviewStep>(\"instructions\");\r\n\r\n  const renderCurrentComponent = () => {\r\n    switch (currentStep) {\r\n      case \"instructions\":\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"finishInterview\")} />\r\n        );\r\n      // case \"questions\":\r\n      //   return <QuestionsPage onNext={() => setCurrentStep(\"recording\")} />;\r\n      case \"finishInterview\":\r\n        return (\r\n          <InterviewRecording\r\n            onNext={() => setCurrentStep(\"finishInterview\")}\r\n          />\r\n        );\r\n      // case \"finishInterview\":\r\n      //   return <FinishInterview onNext={() => setCurrentStep(\"analysis\")} />;\r\n      // case \"analysis\":\r\n      //   return <Analysis />;\r\n      default:\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"finishInterview\")} />\r\n        );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <InterviewProvider>\r\n      <div>{renderCurrentComponent()}</div>\r\n    </InterviewProvider>\r\n  );\r\n};\r\n\r\nexport default Interview;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAIA;AACA;AAPA;;;;;;AAgBA,MAAM,YAAY;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC,iJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAExD,oBAAoB;YACpB,yEAAyE;YACzE,KAAK;gBACH,qBACE,8OAAC,8IAAA,CAAA,UAAkB;oBACjB,QAAQ,IAAM,eAAe;;;;;;YAGnC,0BAA0B;YAC1B,0EAA0E;YAC1E,mBAAmB;YACnB,yBAAyB;YACzB;gBACE,qBACE,8OAAC,iJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;QAE1D;IACF;IAEA,qBACE,8OAAC,4HAAA,CAAA,oBAAiB;kBAChB,cAAA,8OAAC;sBAAK;;;;;;;;;;;AAGZ;uCAEe", "debugId": null}}]}