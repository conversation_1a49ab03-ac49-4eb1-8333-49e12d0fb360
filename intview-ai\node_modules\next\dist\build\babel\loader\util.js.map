{"version": 3, "sources": ["../../../../src/build/babel/loader/util.ts"], "sourcesContent": ["export function consumeIterator(iter: Iterator<any>) {\n  while (true) {\n    const { value, done } = iter.next()\n    if (done) {\n      return value\n    }\n  }\n}\n"], "names": ["consumeIterator", "iter", "value", "done", "next"], "mappings": ";;;;+BAAgBA;;;eAAAA;;;AAAT,SAASA,gBAAgBC,IAAmB;IACjD,MAAO,KAAM;QACX,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGF,KAAKG,IAAI;QACjC,IAAID,MAAM;YACR,OAAOD;QACT;IACF;AACF"}