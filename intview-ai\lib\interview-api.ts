// Interview API service for communicating with interview-ai-server
export interface ConversationMessage {
  role: 'candidate' | 'interviewer';
  content: string;
}

export interface InterviewRequest {
  position: string;
  name: string;
  experience: number;
  history: ConversationMessage[];
}

export interface InterviewResponse {
  response: string;
}

class InterviewApiService {
  private baseUrl: string;

  constructor() {
    // Default to localhost:3000, can be overridden via environment variable
    this.baseUrl = process.env.NEXT_PUBLIC_INTERVIEW_API_URL || 'http://localhost:4000';
  }

  async sendInterviewRequest(request: InterviewRequest): Promise<InterviewResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/interview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Interview API Error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data: InterviewResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Failed to send interview request:', error);
      throw error;
    }
  }

  async startInterview(position: string, name: string, experience: number): Promise<string> {
    const request: InterviewRequest = {
      position,
      name,
      experience,
      history: []
    };

    const response = await this.sendInterviewRequest(request);
    return response.response;
  }

  async continueInterview(
    position: string, 
    name: string, 
    experience: number, 
    history: ConversationMessage[]
  ): Promise<string> {
    const request: InterviewRequest = {
      position,
      name,
      experience,
      history
    };

    const response = await this.sendInterviewRequest(request);
    return response.response;
  }
}

// Export singleton instance
export const interviewApi = new InterviewApiService();
