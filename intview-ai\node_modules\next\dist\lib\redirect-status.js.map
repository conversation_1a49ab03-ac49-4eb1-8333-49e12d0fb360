{"version": 3, "sources": ["../../src/lib/redirect-status.ts"], "sourcesContent": ["import { RedirectStatusCode } from '../client/components/redirect-status-code'\n\nexport const allowedStatusCodes = new Set([301, 302, 303, 307, 308])\n\nexport function getRedirectStatus(route: {\n  statusCode?: number\n  permanent?: boolean\n}): number {\n  return (\n    route.statusCode ||\n    (route.permanent\n      ? RedirectStatusCode.PermanentRedirect\n      : RedirectStatusCode.TemporaryRedirect)\n  )\n}\n\n// for redirects we restrict matching /_next and for all routes\n// we add an optional trailing slash at the end for easier\n// configuring between trailingSlash: true/false\nexport function modifyRouteRegex(regex: string, restrictedPaths?: string[]) {\n  if (restrictedPaths) {\n    regex = regex.replace(\n      /\\^/,\n      `^(?!${restrictedPaths\n        .map((path) => path.replace(/\\//g, '\\\\/'))\n        .join('|')})`\n    )\n  }\n  regex = regex.replace(/\\$$/, '(?:\\\\/)?$')\n  return regex\n}\n"], "names": ["allowedStatusCodes", "getRedirectStatus", "modifyRouteRegex", "Set", "route", "statusCode", "permanent", "RedirectStatusCode", "PermanentRedirect", "TemporaryRedirect", "regex", "restrictedPaths", "replace", "map", "path", "join"], "mappings": ";;;;;;;;;;;;;;;;IAEaA,kBAAkB;eAAlBA;;IAEGC,iBAAiB;eAAjBA;;IAeAC,gBAAgB;eAAhBA;;;oCAnBmB;AAE5B,MAAMF,qBAAqB,IAAIG,IAAI;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI;AAE5D,SAASF,kBAAkBG,KAGjC;IACC,OACEA,MAAMC,UAAU,IACfD,CAAAA,MAAME,SAAS,GACZC,sCAAkB,CAACC,iBAAiB,GACpCD,sCAAkB,CAACE,iBAAiB,AAAD;AAE3C;AAKO,SAASP,iBAAiBQ,KAAa,EAAEC,eAA0B;IACxE,IAAIA,iBAAiB;QACnBD,QAAQA,MAAME,OAAO,CACnB,MACA,CAAC,IAAI,EAAED,gBACJE,GAAG,CAAC,CAACC,OAASA,KAAKF,OAAO,CAAC,OAAO,QAClCG,IAAI,CAAC,KAAK,CAAC,CAAC;IAEnB;IACAL,QAAQA,MAAME,OAAO,CAAC,OAAO;IAC7B,OAAOF;AACT"}