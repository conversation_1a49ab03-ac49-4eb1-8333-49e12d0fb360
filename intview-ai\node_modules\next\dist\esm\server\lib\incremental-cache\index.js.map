{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/index.ts"], "sourcesContent": ["import type { CacheFs } from '../../../shared/lib/utils'\nimport type { PrerenderManifest } from '../../../build'\nimport {\n  type IncrementalCacheValue,\n  type IncrementalCacheEntry,\n  type IncrementalCache as IncrementalCacheType,\n  IncrementalCacheKind,\n  CachedRouteKind,\n  type IncrementalResponseCacheEntry,\n  type IncrementalFetchCacheEntry,\n  type GetIncrementalFetchCacheContext,\n  type GetIncrementalResponseCacheContext,\n  type CachedFetchValue,\n  type SetIncrementalFetchCacheContext,\n  type SetIncrementalResponseCacheContext,\n} from '../../response-cache'\nimport type { DeepReadonly } from '../../../shared/lib/deep-readonly'\n\nimport FileSystemCache from './file-system-cache'\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path'\n\nimport {\n  CACHE_ONE_YEAR,\n  PRERENDER_REVALIDATE_HEADER,\n} from '../../../lib/constants'\nimport { toRoute } from '../to-route'\nimport { SharedCacheControls } from './shared-cache-controls'\nimport {\n  getPrerenderResumeDataCache,\n  getRenderResumeDataCache,\n  workUnitAsyncStorage,\n} from '../../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../../shared/lib/invariant-error'\nimport type { Revalidate } from '../cache-control'\nimport { getPreviouslyRevalidatedTags } from '../../server-utils'\nimport { workAsyncStorage } from '../../app-render/work-async-storage.external'\n\nexport interface CacheHandlerContext {\n  fs?: CacheFs\n  dev?: boolean\n  flushToDisk?: boolean\n  serverDistDir?: string\n  maxMemoryCacheSize?: number\n  fetchCacheKeyPrefix?: string\n  prerenderManifest?: PrerenderManifest\n  revalidatedTags: string[]\n  _requestHeaders: IncrementalCache['requestHeaders']\n}\n\nexport interface CacheHandlerValue {\n  lastModified?: number\n  age?: number\n  cacheState?: string\n  value: IncrementalCacheValue | null\n}\n\nexport class CacheHandler {\n  // eslint-disable-next-line\n  constructor(_ctx: CacheHandlerContext) {}\n\n  public async get(\n    _cacheKey: string,\n    _ctx: GetIncrementalFetchCacheContext | GetIncrementalResponseCacheContext\n  ): Promise<CacheHandlerValue | null> {\n    return {} as any\n  }\n\n  public async set(\n    _cacheKey: string,\n    _data: IncrementalCacheValue | null,\n    _ctx: SetIncrementalFetchCacheContext | SetIncrementalResponseCacheContext\n  ): Promise<void> {}\n\n  public async revalidateTag(\n    ..._args: Parameters<IncrementalCache['revalidateTag']>\n  ): Promise<void> {}\n\n  public resetRequestCache(): void {}\n}\n\nexport class IncrementalCache implements IncrementalCacheType {\n  readonly dev?: boolean\n  readonly disableForTestmode?: boolean\n  readonly cacheHandler?: CacheHandler\n  readonly hasCustomCacheHandler: boolean\n  readonly prerenderManifest: DeepReadonly<PrerenderManifest>\n  readonly requestHeaders: Record<string, undefined | string | string[]>\n  readonly requestProtocol?: 'http' | 'https'\n  readonly allowedRevalidateHeaderKeys?: string[]\n  readonly minimalMode?: boolean\n  readonly fetchCacheKeyPrefix?: string\n  readonly revalidatedTags?: string[]\n  readonly isOnDemandRevalidate?: boolean\n\n  private readonly locks = new Map<string, Promise<void>>()\n\n  /**\n   * The cache controls for routes. This will source the values from the\n   * prerender manifest until the in-memory cache is updated with new values.\n   */\n  private readonly cacheControls: SharedCacheControls\n\n  constructor({\n    fs,\n    dev,\n    flushToDisk,\n    minimalMode,\n    serverDistDir,\n    requestHeaders,\n    requestProtocol,\n    maxMemoryCacheSize,\n    getPrerenderManifest,\n    fetchCacheKeyPrefix,\n    CurCacheHandler,\n    allowedRevalidateHeaderKeys,\n  }: {\n    fs?: CacheFs\n    dev: boolean\n    minimalMode?: boolean\n    serverDistDir?: string\n    flushToDisk?: boolean\n    requestProtocol?: 'http' | 'https'\n    allowedRevalidateHeaderKeys?: string[]\n    requestHeaders: IncrementalCache['requestHeaders']\n    maxMemoryCacheSize?: number\n    getPrerenderManifest: () => DeepReadonly<PrerenderManifest>\n    fetchCacheKeyPrefix?: string\n    CurCacheHandler?: typeof CacheHandler\n  }) {\n    const debug = !!process.env.NEXT_PRIVATE_DEBUG_CACHE\n    this.hasCustomCacheHandler = Boolean(CurCacheHandler)\n\n    const cacheHandlersSymbol = Symbol.for('@next/cache-handlers')\n    const _globalThis: typeof globalThis & {\n      [cacheHandlersSymbol]?: {\n        FetchCache?: typeof CacheHandler\n      }\n    } = globalThis\n\n    if (!CurCacheHandler) {\n      // if we have a global cache handler available leverage it\n      const globalCacheHandler = _globalThis[cacheHandlersSymbol]\n\n      if (globalCacheHandler?.FetchCache) {\n        CurCacheHandler = globalCacheHandler.FetchCache\n      } else {\n        if (fs && serverDistDir) {\n          if (debug) {\n            console.log('using filesystem cache handler')\n          }\n          CurCacheHandler = FileSystemCache\n        }\n      }\n    } else if (debug) {\n      console.log('using custom cache handler', CurCacheHandler.name)\n    }\n\n    if (process.env.__NEXT_TEST_MAX_ISR_CACHE) {\n      // Allow cache size to be overridden for testing purposes\n      maxMemoryCacheSize = parseInt(process.env.__NEXT_TEST_MAX_ISR_CACHE, 10)\n    }\n    this.dev = dev\n    this.disableForTestmode = process.env.NEXT_PRIVATE_TEST_PROXY === 'true'\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] = minimalMode\n    this.requestHeaders = requestHeaders\n    this.requestProtocol = requestProtocol\n    this.allowedRevalidateHeaderKeys = allowedRevalidateHeaderKeys\n    this.prerenderManifest = getPrerenderManifest()\n    this.cacheControls = new SharedCacheControls(this.prerenderManifest)\n    this.fetchCacheKeyPrefix = fetchCacheKeyPrefix\n    let revalidatedTags: string[] = []\n\n    if (\n      requestHeaders[PRERENDER_REVALIDATE_HEADER] ===\n      this.prerenderManifest?.preview?.previewModeId\n    ) {\n      this.isOnDemandRevalidate = true\n    }\n\n    if (minimalMode) {\n      revalidatedTags = getPreviouslyRevalidatedTags(\n        requestHeaders,\n        this.prerenderManifest?.preview?.previewModeId\n      )\n    }\n\n    if (CurCacheHandler) {\n      this.cacheHandler = new CurCacheHandler({\n        dev,\n        fs,\n        flushToDisk,\n        serverDistDir,\n        revalidatedTags,\n        maxMemoryCacheSize,\n        _requestHeaders: requestHeaders,\n        fetchCacheKeyPrefix,\n      })\n    }\n  }\n\n  private calculateRevalidate(\n    pathname: string,\n    fromTime: number,\n    dev: boolean,\n    isFallback: boolean | undefined\n  ): Revalidate {\n    // in development we don't have a prerender-manifest\n    // and default to always revalidating to allow easier debugging\n    if (dev)\n      return Math.floor(performance.timeOrigin + performance.now() - 1000)\n\n    const cacheControl = this.cacheControls.get(toRoute(pathname))\n\n    // if an entry isn't present in routes we fallback to a default\n    // of revalidating after 1 second unless it's a fallback request.\n    const initialRevalidateSeconds = cacheControl\n      ? cacheControl.revalidate\n      : isFallback\n        ? false\n        : 1\n\n    const revalidateAfter =\n      typeof initialRevalidateSeconds === 'number'\n        ? initialRevalidateSeconds * 1000 + fromTime\n        : initialRevalidateSeconds\n\n    return revalidateAfter\n  }\n\n  _getPathname(pathname: string, fetchCache?: boolean) {\n    return fetchCache ? pathname : normalizePagePath(pathname)\n  }\n\n  resetRequestCache() {\n    this.cacheHandler?.resetRequestCache?.()\n  }\n\n  async lock(cacheKey: string) {\n    let unlockNext: () => Promise<void> = () => Promise.resolve()\n    const existingLock = this.locks.get(cacheKey)\n\n    if (existingLock) {\n      await existingLock\n    }\n\n    const newLock = new Promise<void>((resolve) => {\n      unlockNext = async () => {\n        resolve()\n        this.locks.delete(cacheKey) // Remove the lock upon release\n      }\n    })\n\n    this.locks.set(cacheKey, newLock)\n    return unlockNext\n  }\n\n  async revalidateTag(tags: string | string[]): Promise<void> {\n    return this.cacheHandler?.revalidateTag(tags)\n  }\n\n  // x-ref: https://github.com/facebook/react/blob/2655c9354d8e1c54ba888444220f63e836925caa/packages/react/src/ReactFetch.js#L23\n  async generateCacheKey(\n    url: string,\n    init: RequestInit | Request = {}\n  ): Promise<string> {\n    // this should be bumped anytime a fix is made to cache entries\n    // that should bust the cache\n    const MAIN_KEY_PREFIX = 'v3'\n\n    const bodyChunks: string[] = []\n\n    const encoder = new TextEncoder()\n    const decoder = new TextDecoder()\n\n    if (init.body) {\n      // handle ReadableStream body\n      if (typeof (init.body as any).getReader === 'function') {\n        const readableBody = init.body as ReadableStream<Uint8Array | string>\n\n        const chunks: Uint8Array[] = []\n\n        try {\n          await readableBody.pipeTo(\n            new WritableStream({\n              write(chunk) {\n                if (typeof chunk === 'string') {\n                  chunks.push(encoder.encode(chunk))\n                  bodyChunks.push(chunk)\n                } else {\n                  chunks.push(chunk)\n                  bodyChunks.push(decoder.decode(chunk, { stream: true }))\n                }\n              },\n            })\n          )\n\n          // Flush the decoder.\n          bodyChunks.push(decoder.decode())\n\n          // Create a new buffer with all the chunks.\n          const length = chunks.reduce((total, arr) => total + arr.length, 0)\n          const arrayBuffer = new Uint8Array(length)\n\n          // Push each of the chunks into the new array buffer.\n          let offset = 0\n          for (const chunk of chunks) {\n            arrayBuffer.set(chunk, offset)\n            offset += chunk.length\n          }\n\n          ;(init as any)._ogBody = arrayBuffer\n        } catch (err) {\n          console.error('Problem reading body', err)\n        }\n      } // handle FormData or URLSearchParams bodies\n      else if (typeof (init.body as any).keys === 'function') {\n        const formData = init.body as FormData\n        ;(init as any)._ogBody = init.body\n        for (const key of new Set([...formData.keys()])) {\n          const values = formData.getAll(key)\n          bodyChunks.push(\n            `${key}=${(\n              await Promise.all(\n                values.map(async (val) => {\n                  if (typeof val === 'string') {\n                    return val\n                  } else {\n                    return await val.text()\n                  }\n                })\n              )\n            ).join(',')}`\n          )\n        }\n        // handle blob body\n      } else if (typeof (init.body as any).arrayBuffer === 'function') {\n        const blob = init.body as Blob\n        const arrayBuffer = await blob.arrayBuffer()\n        bodyChunks.push(await blob.text())\n        ;(init as any)._ogBody = new Blob([arrayBuffer], { type: blob.type })\n      } else if (typeof init.body === 'string') {\n        bodyChunks.push(init.body)\n        ;(init as any)._ogBody = init.body\n      }\n    }\n\n    const headers =\n      typeof (init.headers || {}).keys === 'function'\n        ? Object.fromEntries(init.headers as Headers)\n        : Object.assign({}, init.headers)\n\n    // w3c trace context headers can break request caching and deduplication\n    // so we remove them from the cache key\n    if ('traceparent' in headers) delete headers['traceparent']\n    if ('tracestate' in headers) delete headers['tracestate']\n\n    const cacheString = JSON.stringify([\n      MAIN_KEY_PREFIX,\n      this.fetchCacheKeyPrefix || '',\n      url,\n      init.method,\n      headers,\n      init.mode,\n      init.redirect,\n      init.credentials,\n      init.referrer,\n      init.referrerPolicy,\n      init.integrity,\n      init.cache,\n      bodyChunks,\n    ])\n\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      function bufferToHex(buffer: ArrayBuffer): string {\n        return Array.prototype.map\n          .call(new Uint8Array(buffer), (b) => b.toString(16).padStart(2, '0'))\n          .join('')\n      }\n      const buffer = encoder.encode(cacheString)\n      return bufferToHex(await crypto.subtle.digest('SHA-256', buffer))\n    } else {\n      const crypto = require('crypto') as typeof import('crypto')\n      return crypto.createHash('sha256').update(cacheString).digest('hex')\n    }\n  }\n\n  async get(\n    cacheKey: string,\n    ctx: GetIncrementalFetchCacheContext\n  ): Promise<IncrementalFetchCacheEntry | null>\n  async get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  async get(\n    cacheKey: string,\n    ctx: GetIncrementalFetchCacheContext | GetIncrementalResponseCacheContext\n  ): Promise<IncrementalCacheEntry | null> {\n    // Unlike other caches if we have a resume data cache, we use it even if\n    // testmode would normally disable it or if requestHeaders say 'no-cache'.\n    if (ctx.kind === IncrementalCacheKind.FETCH) {\n      const workUnitStore = workUnitAsyncStorage.getStore()\n      const resumeDataCache = workUnitStore\n        ? getRenderResumeDataCache(workUnitStore)\n        : null\n      if (resumeDataCache) {\n        const memoryCacheData = resumeDataCache.fetch.get(cacheKey)\n        if (memoryCacheData?.kind === CachedRouteKind.FETCH) {\n          return { isStale: false, value: memoryCacheData }\n        }\n      }\n    }\n\n    // we don't leverage the prerender cache in dev mode\n    // so that getStaticProps is always called for easier debugging\n    if (\n      this.disableForTestmode ||\n      (this.dev &&\n        (ctx.kind !== IncrementalCacheKind.FETCH ||\n          this.requestHeaders['cache-control'] === 'no-cache'))\n    ) {\n      return null\n    }\n\n    cacheKey = this._getPathname(\n      cacheKey,\n      ctx.kind === IncrementalCacheKind.FETCH\n    )\n\n    const cacheData = await this.cacheHandler?.get(cacheKey, ctx)\n\n    if (ctx.kind === IncrementalCacheKind.FETCH) {\n      if (!cacheData) {\n        return null\n      }\n\n      if (cacheData.value?.kind !== CachedRouteKind.FETCH) {\n        throw new InvariantError(\n          `Expected cached value for cache key ${JSON.stringify(cacheKey)} to be a \"FETCH\" kind, got ${JSON.stringify(cacheData.value?.kind)} instead.`\n        )\n      }\n\n      const workStore = workAsyncStorage.getStore()\n      const combinedTags = [...(ctx.tags || []), ...(ctx.softTags || [])]\n      // if a tag was revalidated we don't return stale data\n      if (\n        combinedTags.some(\n          (tag) =>\n            this.revalidatedTags?.includes(tag) ||\n            workStore?.pendingRevalidatedTags?.includes(tag)\n        )\n      ) {\n        return null\n      }\n\n      const revalidate = ctx.revalidate || cacheData.value.revalidate\n      const age =\n        (performance.timeOrigin +\n          performance.now() -\n          (cacheData.lastModified || 0)) /\n        1000\n\n      const isStale = age > revalidate\n      const data = cacheData.value.data\n\n      return {\n        isStale,\n        value: { kind: CachedRouteKind.FETCH, data, revalidate },\n      }\n    } else if (cacheData?.value?.kind === CachedRouteKind.FETCH) {\n      throw new InvariantError(\n        `Expected cached value for cache key ${JSON.stringify(cacheKey)} not to be a ${JSON.stringify(ctx.kind)} kind, got \"FETCH\" instead.`\n      )\n    }\n\n    let entry: IncrementalResponseCacheEntry | null = null\n    const { isFallback } = ctx\n    const cacheControl = this.cacheControls.get(toRoute(cacheKey))\n\n    let isStale: boolean | -1 | undefined\n    let revalidateAfter: Revalidate\n\n    if (cacheData?.lastModified === -1) {\n      isStale = -1\n      revalidateAfter = -1 * CACHE_ONE_YEAR\n    } else {\n      revalidateAfter = this.calculateRevalidate(\n        cacheKey,\n        cacheData?.lastModified || performance.timeOrigin + performance.now(),\n        this.dev ?? false,\n        ctx.isFallback\n      )\n      isStale =\n        revalidateAfter !== false &&\n        revalidateAfter < performance.timeOrigin + performance.now()\n          ? true\n          : undefined\n    }\n\n    if (cacheData) {\n      entry = {\n        isStale,\n        cacheControl,\n        revalidateAfter,\n        value: cacheData.value,\n        isFallback,\n      }\n    }\n\n    if (\n      !cacheData &&\n      this.prerenderManifest.notFoundRoutes.includes(cacheKey)\n    ) {\n      // for the first hit after starting the server the cache\n      // may not have a way to save notFound: true so if\n      // the prerender-manifest marks this as notFound then we\n      // return that entry and trigger a cache set to give it a\n      // chance to update in-memory entries\n      entry = {\n        isStale,\n        value: null,\n        cacheControl,\n        revalidateAfter,\n        isFallback,\n      }\n      this.set(cacheKey, entry.value, { ...ctx, cacheControl })\n    }\n    return entry\n  }\n\n  async set(\n    pathname: string,\n    data: CachedFetchValue | null,\n    ctx: SetIncrementalFetchCacheContext\n  ): Promise<void>\n  async set(\n    pathname: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n  async set(\n    pathname: string,\n    data: IncrementalCacheValue | null,\n    ctx: SetIncrementalFetchCacheContext | SetIncrementalResponseCacheContext\n  ): Promise<void> {\n    // Even if we otherwise disable caching for testMode or if no fetchCache is\n    // configured we still always stash results in the resume data cache if one\n    // exists. This is because this is a transient in memory cache that\n    // populates caches ahead of a dynamic render in dev mode to allow the RSC\n    // debug info to have the right environment associated to it.\n    if (data?.kind === CachedRouteKind.FETCH) {\n      const workUnitStore = workUnitAsyncStorage.getStore()\n      const prerenderResumeDataCache = workUnitStore\n        ? getPrerenderResumeDataCache(workUnitStore)\n        : null\n      if (prerenderResumeDataCache) {\n        prerenderResumeDataCache.fetch.set(pathname, data)\n      }\n    }\n\n    if (this.disableForTestmode || (this.dev && !ctx.fetchCache)) return\n\n    pathname = this._getPathname(pathname, ctx.fetchCache)\n\n    // FetchCache has upper limit of 2MB per-entry currently\n    const itemSize = JSON.stringify(data).length\n    if (\n      ctx.fetchCache &&\n      // we don't show this error/warning when a custom cache handler is being used\n      // as it might not have this limit\n      !this.hasCustomCacheHandler &&\n      itemSize > 2 * 1024 * 1024\n    ) {\n      if (this.dev) {\n        throw new Error(\n          `Failed to set Next.js data cache, items over 2MB can not be cached (${itemSize} bytes)`\n        )\n      }\n      return\n    }\n\n    try {\n      if (!ctx.fetchCache && ctx.cacheControl) {\n        this.cacheControls.set(toRoute(pathname), ctx.cacheControl)\n      }\n\n      await this.cacheHandler?.set(pathname, data, ctx)\n    } catch (error) {\n      console.warn('Failed to update prerender cache for', pathname, error)\n    }\n  }\n}\n"], "names": ["IncrementalCacheKind", "CachedRouteKind", "FileSystemCache", "normalizePagePath", "CACHE_ONE_YEAR", "PRERENDER_REVALIDATE_HEADER", "toRoute", "SharedCacheControls", "getPrerenderResumeDataCache", "getRenderResumeDataCache", "workUnitAsyncStorage", "InvariantError", "getPreviouslyRevalidatedTags", "workAsyncStorage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_ctx", "get", "_cacheKey", "set", "_data", "revalidateTag", "_args", "resetRequestCache", "IncrementalCache", "fs", "dev", "flushToDisk", "minimalMode", "serverDistDir", "requestHeaders", "requestProtocol", "maxMemoryCacheSize", "getPrerenderManifest", "fetchCacheKeyPrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedRevalidateHeaderKeys", "locks", "Map", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "hasCustomCacheHandler", "Boolean", "cacheHandlersSymbol", "Symbol", "for", "_globalThis", "globalThis", "globalCacheHandler", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "name", "__NEXT_TEST_MAX_ISR_CACHE", "parseInt", "disableForTestmode", "NEXT_PRIVATE_TEST_PROXY", "minimalModeKey", "prerenderManifest", "cacheControls", "revalidatedTags", "preview", "previewModeId", "isOnDemandRevalidate", "cache<PERSON><PERSON><PERSON>", "_requestHeaders", "calculateRevalidate", "pathname", "fromTime", "<PERSON><PERSON><PERSON><PERSON>", "Math", "floor", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "cacheControl", "initialRevalidateSeconds", "revalidate", "revalidateAfter", "_getPathname", "fetchCache", "lock", "cache<PERSON>ey", "unlockNext", "Promise", "resolve", "existingLock", "newLock", "delete", "tags", "generate<PERSON>ache<PERSON>ey", "url", "init", "MAIN_KEY_PREFIX", "bodyChunks", "encoder", "TextEncoder", "decoder", "TextDecoder", "body", "<PERSON><PERSON><PERSON><PERSON>", "readableBody", "chunks", "pipeTo", "WritableStream", "write", "chunk", "push", "encode", "decode", "stream", "length", "reduce", "total", "arr", "arrayBuffer", "Uint8Array", "offset", "_ogBody", "err", "error", "keys", "formData", "key", "Set", "values", "getAll", "all", "map", "val", "text", "join", "blob", "Blob", "type", "headers", "Object", "fromEntries", "assign", "cacheString", "JSON", "stringify", "method", "mode", "redirect", "credentials", "referrer", "referrerPolicy", "integrity", "cache", "NEXT_RUNTIME", "bufferToHex", "buffer", "Array", "prototype", "call", "b", "toString", "padStart", "crypto", "subtle", "digest", "require", "createHash", "update", "ctx", "cacheData", "kind", "FETCH", "workUnitStore", "getStore", "resumeDataCache", "memoryCacheData", "fetch", "isStale", "value", "workStore", "combinedTags", "softTags", "some", "tag", "includes", "pendingRevalidatedTags", "age", "lastModified", "data", "entry", "undefined", "notFoundRoutes", "prerenderResumeDataCache", "itemSize", "Error", "warn"], "mappings": "AAEA,SAIEA,oBAAoB,EACpBC,eAAe,QAQV,uBAAsB;AAG7B,OAAOC,qBAAqB,sBAAqB;AACjD,SAASC,iBAAiB,QAAQ,oDAAmD;AAErF,SACEC,cAAc,EACdC,2BAA2B,QACtB,yBAAwB;AAC/B,SAASC,OAAO,QAAQ,cAAa;AACrC,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SACEC,2BAA2B,EAC3BC,wBAAwB,EACxBC,oBAAoB,QACf,oDAAmD;AAC1D,SAASC,cAAc,QAAQ,sCAAqC;AAEpE,SAASC,4BAA4B,QAAQ,qBAAoB;AACjE,SAASC,gBAAgB,QAAQ,+CAA8C;AAqB/E,OAAO,MAAMC;IACX,2BAA2B;IAC3BC,YAAYC,IAAyB,CAAE,CAAC;IAExC,MAAaC,IACXC,SAAiB,EACjBF,IAA0E,EACvC;QACnC,OAAO,CAAC;IACV;IAEA,MAAaG,IACXD,SAAiB,EACjBE,KAAmC,EACnCJ,IAA0E,EAC3D,CAAC;IAElB,MAAaK,cACX,GAAGC,KAAoD,EACxC,CAAC;IAEXC,oBAA0B,CAAC;AACpC;AAEA,OAAO,MAAMC;IAsBXT,YAAY,EACVU,EAAE,EACFC,GAAG,EACHC,WAAW,EACXC,WAAW,EACXC,aAAa,EACbC,cAAc,EACdC,eAAe,EACfC,kBAAkB,EAClBC,oBAAoB,EACpBC,mBAAmB,EACnBC,eAAe,EACfC,2BAA2B,EAc5B,CAAE;YAiDC,iCAAA;aAnFaC,QAAQ,IAAIC;QAmC3B,MAAMC,QAAQ,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;QACpD,IAAI,CAACC,qBAAqB,GAAGC,QAAQT;QAErC,MAAMU,sBAAsBC,OAAOC,GAAG,CAAC;QACvC,MAAMC,cAIFC;QAEJ,IAAI,CAACd,iBAAiB;YACpB,0DAA0D;YAC1D,MAAMe,qBAAqBF,WAAW,CAACH,oBAAoB;YAE3D,IAAIK,sCAAAA,mBAAoBC,UAAU,EAAE;gBAClChB,kBAAkBe,mBAAmBC,UAAU;YACjD,OAAO;gBACL,IAAI1B,MAAMI,eAAe;oBACvB,IAAIU,OAAO;wBACTa,QAAQC,GAAG,CAAC;oBACd;oBACAlB,kBAAkBjC;gBACpB;YACF;QACF,OAAO,IAAIqC,OAAO;YAChBa,QAAQC,GAAG,CAAC,8BAA8BlB,gBAAgBmB,IAAI;QAChE;QAEA,IAAId,QAAQC,GAAG,CAACc,yBAAyB,EAAE;YACzC,yDAAyD;YACzDvB,qBAAqBwB,SAAShB,QAAQC,GAAG,CAACc,yBAAyB,EAAE;QACvE;QACA,IAAI,CAAC7B,GAAG,GAAGA;QACX,IAAI,CAAC+B,kBAAkB,GAAGjB,QAAQC,GAAG,CAACiB,uBAAuB,KAAK;QAClE,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAAG/B;QACvB,IAAI,CAACE,cAAc,GAAGA;QACtB,IAAI,CAACC,eAAe,GAAGA;QACvB,IAAI,CAACK,2BAA2B,GAAGA;QACnC,IAAI,CAACwB,iBAAiB,GAAG3B;QACzB,IAAI,CAAC4B,aAAa,GAAG,IAAItD,oBAAoB,IAAI,CAACqD,iBAAiB;QACnE,IAAI,CAAC1B,mBAAmB,GAAGA;QAC3B,IAAI4B,kBAA4B,EAAE;QAElC,IACEhC,cAAc,CAACzB,4BAA4B,OAC3C,0BAAA,IAAI,CAACuD,iBAAiB,sBAAtB,kCAAA,wBAAwBG,OAAO,qBAA/B,gCAAiCC,aAAa,GAC9C;YACA,IAAI,CAACC,oBAAoB,GAAG;QAC9B;QAEA,IAAIrC,aAAa;gBAGb,kCAAA;YAFFkC,kBAAkBlD,6BAChBkB,iBACA,2BAAA,IAAI,CAAC8B,iBAAiB,sBAAtB,mCAAA,yBAAwBG,OAAO,qBAA/B,iCAAiCC,aAAa;QAElD;QAEA,IAAI7B,iBAAiB;YACnB,IAAI,CAAC+B,YAAY,GAAG,IAAI/B,gBAAgB;gBACtCT;gBACAD;gBACAE;gBACAE;gBACAiC;gBACA9B;gBACAmC,iBAAiBrC;gBACjBI;YACF;QACF;IACF;IAEQkC,oBACNC,QAAgB,EAChBC,QAAgB,EAChB5C,GAAY,EACZ6C,UAA+B,EACnB;QACZ,oDAAoD;QACpD,+DAA+D;QAC/D,IAAI7C,KACF,OAAO8C,KAAKC,KAAK,CAACC,YAAYC,UAAU,GAAGD,YAAYE,GAAG,KAAK;QAEjE,MAAMC,eAAe,IAAI,CAAChB,aAAa,CAAC5C,GAAG,CAACX,QAAQ+D;QAEpD,+DAA+D;QAC/D,iEAAiE;QACjE,MAAMS,2BAA2BD,eAC7BA,aAAaE,UAAU,GACvBR,aACE,QACA;QAEN,MAAMS,kBACJ,OAAOF,6BAA6B,WAChCA,2BAA2B,OAAOR,WAClCQ;QAEN,OAAOE;IACT;IAEAC,aAAaZ,QAAgB,EAAEa,UAAoB,EAAE;QACnD,OAAOA,aAAab,WAAWlE,kBAAkBkE;IACnD;IAEA9C,oBAAoB;YAClB,sCAAA;SAAA,qBAAA,IAAI,CAAC2C,YAAY,sBAAjB,uCAAA,mBAAmB3C,iBAAiB,qBAApC,0CAAA;IACF;IAEA,MAAM4D,KAAKC,QAAgB,EAAE;QAC3B,IAAIC,aAAkC,IAAMC,QAAQC,OAAO;QAC3D,MAAMC,eAAe,IAAI,CAACnD,KAAK,CAACpB,GAAG,CAACmE;QAEpC,IAAII,cAAc;YAChB,MAAMA;QACR;QAEA,MAAMC,UAAU,IAAIH,QAAc,CAACC;YACjCF,aAAa;gBACXE;gBACA,IAAI,CAAClD,KAAK,CAACqD,MAAM,CAACN,UAAU,+BAA+B;;YAC7D;QACF;QAEA,IAAI,CAAC/C,KAAK,CAAClB,GAAG,CAACiE,UAAUK;QACzB,OAAOJ;IACT;IAEA,MAAMhE,cAAcsE,IAAuB,EAAiB;YACnD;QAAP,QAAO,qBAAA,IAAI,CAACzB,YAAY,qBAAjB,mBAAmB7C,aAAa,CAACsE;IAC1C;IAEA,8HAA8H;IAC9H,MAAMC,iBACJC,GAAW,EACXC,OAA8B,CAAC,CAAC,EACf;QACjB,+DAA+D;QAC/D,6BAA6B;QAC7B,MAAMC,kBAAkB;QAExB,MAAMC,aAAuB,EAAE;QAE/B,MAAMC,UAAU,IAAIC;QACpB,MAAMC,UAAU,IAAIC;QAEpB,IAAIN,KAAKO,IAAI,EAAE;YACb,6BAA6B;YAC7B,IAAI,OAAO,AAACP,KAAKO,IAAI,CAASC,SAAS,KAAK,YAAY;gBACtD,MAAMC,eAAeT,KAAKO,IAAI;gBAE9B,MAAMG,SAAuB,EAAE;gBAE/B,IAAI;oBACF,MAAMD,aAAaE,MAAM,CACvB,IAAIC,eAAe;wBACjBC,OAAMC,KAAK;4BACT,IAAI,OAAOA,UAAU,UAAU;gCAC7BJ,OAAOK,IAAI,CAACZ,QAAQa,MAAM,CAACF;gCAC3BZ,WAAWa,IAAI,CAACD;4BAClB,OAAO;gCACLJ,OAAOK,IAAI,CAACD;gCACZZ,WAAWa,IAAI,CAACV,QAAQY,MAAM,CAACH,OAAO;oCAAEI,QAAQ;gCAAK;4BACvD;wBACF;oBACF;oBAGF,qBAAqB;oBACrBhB,WAAWa,IAAI,CAACV,QAAQY,MAAM;oBAE9B,2CAA2C;oBAC3C,MAAME,SAAST,OAAOU,MAAM,CAAC,CAACC,OAAOC,MAAQD,QAAQC,IAAIH,MAAM,EAAE;oBACjE,MAAMI,cAAc,IAAIC,WAAWL;oBAEnC,qDAAqD;oBACrD,IAAIM,SAAS;oBACb,KAAK,MAAMX,SAASJ,OAAQ;wBAC1Ba,YAAYlG,GAAG,CAACyF,OAAOW;wBACvBA,UAAUX,MAAMK,MAAM;oBACxB;;oBAEEnB,KAAa0B,OAAO,GAAGH;gBAC3B,EAAE,OAAOI,KAAK;oBACZrE,QAAQsE,KAAK,CAAC,wBAAwBD;gBACxC;YACF,OACK,IAAI,OAAO,AAAC3B,KAAKO,IAAI,CAASsB,IAAI,KAAK,YAAY;gBACtD,MAAMC,WAAW9B,KAAKO,IAAI;gBACxBP,KAAa0B,OAAO,GAAG1B,KAAKO,IAAI;gBAClC,KAAK,MAAMwB,OAAO,IAAIC,IAAI;uBAAIF,SAASD,IAAI;iBAAG,EAAG;oBAC/C,MAAMI,SAASH,SAASI,MAAM,CAACH;oBAC/B7B,WAAWa,IAAI,CACb,GAAGgB,IAAI,CAAC,EAAE,AACR,CAAA,MAAMvC,QAAQ2C,GAAG,CACfF,OAAOG,GAAG,CAAC,OAAOC;wBAChB,IAAI,OAAOA,QAAQ,UAAU;4BAC3B,OAAOA;wBACT,OAAO;4BACL,OAAO,MAAMA,IAAIC,IAAI;wBACvB;oBACF,GACF,EACAC,IAAI,CAAC,MAAM;gBAEjB;YACA,mBAAmB;YACrB,OAAO,IAAI,OAAO,AAACvC,KAAKO,IAAI,CAASgB,WAAW,KAAK,YAAY;gBAC/D,MAAMiB,OAAOxC,KAAKO,IAAI;gBACtB,MAAMgB,cAAc,MAAMiB,KAAKjB,WAAW;gBAC1CrB,WAAWa,IAAI,CAAC,MAAMyB,KAAKF,IAAI;gBAC7BtC,KAAa0B,OAAO,GAAG,IAAIe,KAAK;oBAAClB;iBAAY,EAAE;oBAAEmB,MAAMF,KAAKE,IAAI;gBAAC;YACrE,OAAO,IAAI,OAAO1C,KAAKO,IAAI,KAAK,UAAU;gBACxCL,WAAWa,IAAI,CAACf,KAAKO,IAAI;gBACvBP,KAAa0B,OAAO,GAAG1B,KAAKO,IAAI;YACpC;QACF;QAEA,MAAMoC,UACJ,OAAO,AAAC3C,CAAAA,KAAK2C,OAAO,IAAI,CAAC,CAAA,EAAGd,IAAI,KAAK,aACjCe,OAAOC,WAAW,CAAC7C,KAAK2C,OAAO,IAC/BC,OAAOE,MAAM,CAAC,CAAC,GAAG9C,KAAK2C,OAAO;QAEpC,wEAAwE;QACxE,uCAAuC;QACvC,IAAI,iBAAiBA,SAAS,OAAOA,OAAO,CAAC,cAAc;QAC3D,IAAI,gBAAgBA,SAAS,OAAOA,OAAO,CAAC,aAAa;QAEzD,MAAMI,cAAcC,KAAKC,SAAS,CAAC;YACjChD;YACA,IAAI,CAAC7D,mBAAmB,IAAI;YAC5B2D;YACAC,KAAKkD,MAAM;YACXP;YACA3C,KAAKmD,IAAI;YACTnD,KAAKoD,QAAQ;YACbpD,KAAKqD,WAAW;YAChBrD,KAAKsD,QAAQ;YACbtD,KAAKuD,cAAc;YACnBvD,KAAKwD,SAAS;YACdxD,KAAKyD,KAAK;YACVvD;SACD;QAED,IAAIxD,QAAQC,GAAG,CAAC+G,YAAY,KAAK,QAAQ;YACvC,SAASC,YAAYC,MAAmB;gBACtC,OAAOC,MAAMC,SAAS,CAAC1B,GAAG,CACvB2B,IAAI,CAAC,IAAIvC,WAAWoC,SAAS,CAACI,IAAMA,EAAEC,QAAQ,CAAC,IAAIC,QAAQ,CAAC,GAAG,MAC/D3B,IAAI,CAAC;YACV;YACA,MAAMqB,SAASzD,QAAQa,MAAM,CAAC+B;YAC9B,OAAOY,YAAY,MAAMQ,OAAOC,MAAM,CAACC,MAAM,CAAC,WAAWT;QAC3D,OAAO;YACL,MAAMO,UAASG,QAAQ;YACvB,OAAOH,QAAOI,UAAU,CAAC,UAAUC,MAAM,CAACzB,aAAasB,MAAM,CAAC;QAChE;IACF;IAUA,MAAMlJ,IACJmE,QAAgB,EAChBmF,GAAyE,EAClC;YAgCf,oBAwCbC;QAvEX,wEAAwE;QACxE,0EAA0E;QAC1E,IAAID,IAAIE,IAAI,KAAKzK,qBAAqB0K,KAAK,EAAE;YAC3C,MAAMC,gBAAgBjK,qBAAqBkK,QAAQ;YACnD,MAAMC,kBAAkBF,gBACpBlK,yBAAyBkK,iBACzB;YACJ,IAAIE,iBAAiB;gBACnB,MAAMC,kBAAkBD,gBAAgBE,KAAK,CAAC9J,GAAG,CAACmE;gBAClD,IAAI0F,CAAAA,mCAAAA,gBAAiBL,IAAI,MAAKxK,gBAAgByK,KAAK,EAAE;oBACnD,OAAO;wBAAEM,SAAS;wBAAOC,OAAOH;oBAAgB;gBAClD;YACF;QACF;QAEA,oDAAoD;QACpD,+DAA+D;QAC/D,IACE,IAAI,CAACrH,kBAAkB,IACtB,IAAI,CAAC/B,GAAG,IACN6I,CAAAA,IAAIE,IAAI,KAAKzK,qBAAqB0K,KAAK,IACtC,IAAI,CAAC5I,cAAc,CAAC,gBAAgB,KAAK,UAAS,GACtD;YACA,OAAO;QACT;QAEAsD,WAAW,IAAI,CAACH,YAAY,CAC1BG,UACAmF,IAAIE,IAAI,KAAKzK,qBAAqB0K,KAAK;QAGzC,MAAMF,YAAY,QAAM,qBAAA,IAAI,CAACtG,YAAY,qBAAjB,mBAAmBjD,GAAG,CAACmE,UAAUmF;QAEzD,IAAIA,IAAIE,IAAI,KAAKzK,qBAAqB0K,KAAK,EAAE;gBAKvCF;YAJJ,IAAI,CAACA,WAAW;gBACd,OAAO;YACT;YAEA,IAAIA,EAAAA,oBAAAA,UAAUS,KAAK,qBAAfT,kBAAiBC,IAAI,MAAKxK,gBAAgByK,KAAK,EAAE;oBAE2DF;gBAD9G,MAAM,qBAEL,CAFK,IAAI7J,eACR,CAAC,oCAAoC,EAAEmI,KAAKC,SAAS,CAAC3D,UAAU,2BAA2B,EAAE0D,KAAKC,SAAS,EAACyB,oBAAAA,UAAUS,KAAK,qBAAfT,kBAAiBC,IAAI,EAAE,SAAS,CAAC,GADzI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAMS,YAAYrK,iBAAiB+J,QAAQ;YAC3C,MAAMO,eAAe;mBAAKZ,IAAI5E,IAAI,IAAI,EAAE;mBAAO4E,IAAIa,QAAQ,IAAI,EAAE;aAAE;YACnE,sDAAsD;YACtD,IACED,aAAaE,IAAI,CACf,CAACC;oBACC,uBACAJ;uBADA,EAAA,wBAAA,IAAI,CAACpH,eAAe,qBAApB,sBAAsByH,QAAQ,CAACD,UAC/BJ,8BAAAA,oCAAAA,UAAWM,sBAAsB,qBAAjCN,kCAAmCK,QAAQ,CAACD;gBAEhD;gBACA,OAAO;YACT;YAEA,MAAMvG,aAAawF,IAAIxF,UAAU,IAAIyF,UAAUS,KAAK,CAAClG,UAAU;YAC/D,MAAM0G,MACJ,AAAC/G,CAAAA,YAAYC,UAAU,GACrBD,YAAYE,GAAG,KACd4F,CAAAA,UAAUkB,YAAY,IAAI,CAAA,CAAC,IAC9B;YAEF,MAAMV,UAAUS,MAAM1G;YACtB,MAAM4G,OAAOnB,UAAUS,KAAK,CAACU,IAAI;YAEjC,OAAO;gBACLX;gBACAC,OAAO;oBAAER,MAAMxK,gBAAgByK,KAAK;oBAAEiB;oBAAM5G;gBAAW;YACzD;QACF,OAAO,IAAIyF,CAAAA,8BAAAA,mBAAAA,UAAWS,KAAK,qBAAhBT,iBAAkBC,IAAI,MAAKxK,gBAAgByK,KAAK,EAAE;YAC3D,MAAM,qBAEL,CAFK,IAAI/J,eACR,CAAC,oCAAoC,EAAEmI,KAAKC,SAAS,CAAC3D,UAAU,aAAa,EAAE0D,KAAKC,SAAS,CAACwB,IAAIE,IAAI,EAAE,2BAA2B,CAAC,GADhI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAImB,QAA8C;QAClD,MAAM,EAAErH,UAAU,EAAE,GAAGgG;QACvB,MAAM1F,eAAe,IAAI,CAAChB,aAAa,CAAC5C,GAAG,CAACX,QAAQ8E;QAEpD,IAAI4F;QACJ,IAAIhG;QAEJ,IAAIwF,CAAAA,6BAAAA,UAAWkB,YAAY,MAAK,CAAC,GAAG;YAClCV,UAAU,CAAC;YACXhG,kBAAkB,CAAC,IAAI5E;QACzB,OAAO;YACL4E,kBAAkB,IAAI,CAACZ,mBAAmB,CACxCgB,UACAoF,CAAAA,6BAAAA,UAAWkB,YAAY,KAAIhH,YAAYC,UAAU,GAAGD,YAAYE,GAAG,IACnE,IAAI,CAAClD,GAAG,IAAI,OACZ6I,IAAIhG,UAAU;YAEhByG,UACEhG,oBAAoB,SACpBA,kBAAkBN,YAAYC,UAAU,GAAGD,YAAYE,GAAG,KACtD,OACAiH;QACR;QAEA,IAAIrB,WAAW;YACboB,QAAQ;gBACNZ;gBACAnG;gBACAG;gBACAiG,OAAOT,UAAUS,KAAK;gBACtB1G;YACF;QACF;QAEA,IACE,CAACiG,aACD,IAAI,CAAC5G,iBAAiB,CAACkI,cAAc,CAACP,QAAQ,CAACnG,WAC/C;YACA,wDAAwD;YACxD,kDAAkD;YAClD,wDAAwD;YACxD,yDAAyD;YACzD,qCAAqC;YACrCwG,QAAQ;gBACNZ;gBACAC,OAAO;gBACPpG;gBACAG;gBACAT;YACF;YACA,IAAI,CAACpD,GAAG,CAACiE,UAAUwG,MAAMX,KAAK,EAAE;gBAAE,GAAGV,GAAG;gBAAE1F;YAAa;QACzD;QACA,OAAO+G;IACT;IAYA,MAAMzK,IACJkD,QAAgB,EAChBsH,IAAkC,EAClCpB,GAAyE,EAC1D;QACf,2EAA2E;QAC3E,2EAA2E;QAC3E,mEAAmE;QACnE,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAIoB,CAAAA,wBAAAA,KAAMlB,IAAI,MAAKxK,gBAAgByK,KAAK,EAAE;YACxC,MAAMC,gBAAgBjK,qBAAqBkK,QAAQ;YACnD,MAAMmB,2BAA2BpB,gBAC7BnK,4BAA4BmK,iBAC5B;YACJ,IAAIoB,0BAA0B;gBAC5BA,yBAAyBhB,KAAK,CAAC5J,GAAG,CAACkD,UAAUsH;YAC/C;QACF;QAEA,IAAI,IAAI,CAAClI,kBAAkB,IAAK,IAAI,CAAC/B,GAAG,IAAI,CAAC6I,IAAIrF,UAAU,EAAG;QAE9Db,WAAW,IAAI,CAACY,YAAY,CAACZ,UAAUkG,IAAIrF,UAAU;QAErD,wDAAwD;QACxD,MAAM8G,WAAWlD,KAAKC,SAAS,CAAC4C,MAAM1E,MAAM;QAC5C,IACEsD,IAAIrF,UAAU,IACd,6EAA6E;QAC7E,kCAAkC;QAClC,CAAC,IAAI,CAACvC,qBAAqB,IAC3BqJ,WAAW,IAAI,OAAO,MACtB;YACA,IAAI,IAAI,CAACtK,GAAG,EAAE;gBACZ,MAAM,qBAEL,CAFK,IAAIuK,MACR,CAAC,oEAAoE,EAAED,SAAS,OAAO,CAAC,GADpF,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA;QACF;QAEA,IAAI;gBAKI;YAJN,IAAI,CAACzB,IAAIrF,UAAU,IAAIqF,IAAI1F,YAAY,EAAE;gBACvC,IAAI,CAAChB,aAAa,CAAC1C,GAAG,CAACb,QAAQ+D,WAAWkG,IAAI1F,YAAY;YAC5D;YAEA,QAAM,qBAAA,IAAI,CAACX,YAAY,qBAAjB,mBAAmB/C,GAAG,CAACkD,UAAUsH,MAAMpB;QAC/C,EAAE,OAAO7C,OAAO;YACdtE,QAAQ8I,IAAI,CAAC,wCAAwC7H,UAAUqD;QACjE;IACF;AACF"}