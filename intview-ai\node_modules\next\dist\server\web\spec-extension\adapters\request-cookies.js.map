{"version": 3, "sources": ["../../../../../src/server/web/spec-extension/adapters/request-cookies.ts"], "sourcesContent": ["import { RequestCookies } from '../cookies'\n\nimport { ResponseCookies } from '../cookies'\nimport { ReflectAdapter } from './reflect'\nimport { workAsyncStorage } from '../../../app-render/work-async-storage.external'\nimport {\n  getExpectedRequestStore,\n  type RequestStore,\n} from '../../../app-render/work-unit-async-storage.external'\n\n/**\n * @internal\n */\nexport class ReadonlyRequestCookiesError extends Error {\n  constructor() {\n    super(\n      'Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyRequestCookiesError()\n  }\n}\n\n// We use this to type some APIs but we don't construct instances directly\nexport type { ResponseCookies }\n\n// The `cookies()` API is a mix of request and response cookies. For `.get()` methods,\n// we want to return the request cookie if it exists. For mutative methods like `.set()`,\n// we want to return the response cookie.\nexport type ReadonlyRequestCookies = Omit<\n  RequestCookies,\n  'set' | 'clear' | 'delete'\n> &\n  Pick<ResponseCookies, 'set' | 'delete'>\n\nexport class RequestCookiesAdapter {\n  public static seal(cookies: RequestCookies): ReadonlyRequestCookies {\n    return new Proxy(cookies as any, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'clear':\n          case 'delete':\n          case 'set':\n            return ReadonlyRequestCookiesError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n}\n\nconst SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for('next.mutated.cookies')\n\nexport function getModifiedCookieValues(\n  cookies: ResponseCookies\n): ResponseCookie[] {\n  const modified: ResponseCookie[] | undefined = (cookies as unknown as any)[\n    SYMBOL_MODIFY_COOKIE_VALUES\n  ]\n  if (!modified || !Array.isArray(modified) || modified.length === 0) {\n    return []\n  }\n\n  return modified\n}\n\ntype SetCookieArgs =\n  | [key: string, value: string, cookie?: Partial<ResponseCookie>]\n  | [options: ResponseCookie]\n\nexport function appendMutableCookies(\n  headers: Headers,\n  mutableCookies: ResponseCookies\n): boolean {\n  const modifiedCookieValues = getModifiedCookieValues(mutableCookies)\n  if (modifiedCookieValues.length === 0) {\n    return false\n  }\n\n  // Return a new response that extends the response with\n  // the modified cookies as fallbacks. `res` cookies\n  // will still take precedence.\n  const resCookies = new ResponseCookies(headers)\n  const returnedCookies = resCookies.getAll()\n\n  // Set the modified cookies as fallbacks.\n  for (const cookie of modifiedCookieValues) {\n    resCookies.set(cookie)\n  }\n\n  // Set the original cookies as the final values.\n  for (const cookie of returnedCookies) {\n    resCookies.set(cookie)\n  }\n\n  return true\n}\n\ntype ResponseCookie = NonNullable<\n  ReturnType<InstanceType<typeof ResponseCookies>['get']>\n>\n\nexport class MutableRequestCookiesAdapter {\n  public static wrap(\n    cookies: RequestCookies,\n    onUpdateCookies?: (cookies: string[]) => void\n  ): ResponseCookies {\n    const responseCookies = new ResponseCookies(new Headers())\n    for (const cookie of cookies.getAll()) {\n      responseCookies.set(cookie)\n    }\n\n    let modifiedValues: ResponseCookie[] = []\n    const modifiedCookies = new Set<string>()\n    const updateResponseCookies = () => {\n      // TODO-APP: change method of getting workStore\n      const workStore = workAsyncStorage.getStore()\n      if (workStore) {\n        workStore.pathWasRevalidated = true\n      }\n\n      const allCookies = responseCookies.getAll()\n      modifiedValues = allCookies.filter((c) => modifiedCookies.has(c.name))\n      if (onUpdateCookies) {\n        const serializedCookies: string[] = []\n        for (const cookie of modifiedValues) {\n          const tempCookies = new ResponseCookies(new Headers())\n          tempCookies.set(cookie)\n          serializedCookies.push(tempCookies.toString())\n        }\n\n        onUpdateCookies(serializedCookies)\n      }\n    }\n\n    const wrappedCookies = new Proxy(responseCookies, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          // A special symbol to get the modified cookie values\n          case SYMBOL_MODIFY_COOKIE_VALUES:\n            return modifiedValues\n\n          // TODO: Throw error if trying to set a cookie after the response\n          // headers have been set.\n          case 'delete':\n            return function (...args: [string] | [ResponseCookie]) {\n              modifiedCookies.add(\n                typeof args[0] === 'string' ? args[0] : args[0].name\n              )\n              try {\n                target.delete(...args)\n                return wrappedCookies\n              } finally {\n                updateResponseCookies()\n              }\n            }\n          case 'set':\n            return function (...args: SetCookieArgs) {\n              modifiedCookies.add(\n                typeof args[0] === 'string' ? args[0] : args[0].name\n              )\n              try {\n                target.set(...args)\n                return wrappedCookies\n              } finally {\n                updateResponseCookies()\n              }\n            }\n\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n\n    return wrappedCookies\n  }\n}\n\nexport function wrapWithMutableAccessCheck(\n  responseCookies: ResponseCookies\n): ResponseCookies {\n  const wrappedCookies = new Proxy(responseCookies, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case 'delete':\n          return function (...args: [string] | [ResponseCookie]) {\n            ensureCookiesAreStillMutable('cookies().delete')\n            target.delete(...args)\n            return wrappedCookies\n          }\n        case 'set':\n          return function (...args: SetCookieArgs) {\n            ensureCookiesAreStillMutable('cookies().set')\n            target.set(...args)\n            return wrappedCookies\n          }\n\n        default:\n          return ReflectAdapter.get(target, prop, receiver)\n      }\n    },\n  })\n  return wrappedCookies\n}\n\nexport function areCookiesMutableInCurrentPhase(requestStore: RequestStore) {\n  return requestStore.phase === 'action'\n}\n\n/** Ensure that cookies() starts throwing on mutation\n * if we changed phases and can no longer mutate.\n *\n * This can happen when going:\n *   'render' -> 'after'\n *   'action' -> 'render'\n * */\nfunction ensureCookiesAreStillMutable(callingExpression: string) {\n  const requestStore = getExpectedRequestStore(callingExpression)\n  if (!areCookiesMutableInCurrentPhase(requestStore)) {\n    // TODO: maybe we can give a more precise error message based on callingExpression?\n    throw new ReadonlyRequestCookiesError()\n  }\n}\n\nexport function responseCookiesToRequestCookies(\n  responseCookies: ResponseCookies\n): RequestCookies {\n  const requestCookies = new RequestCookies(new Headers())\n  for (const cookie of responseCookies.getAll()) {\n    requestCookies.set(cookie)\n  }\n  return requestCookies\n}\n"], "names": ["MutableRequestCookiesAdapter", "ReadonlyRequestCookiesError", "RequestCookiesAdapter", "appendMutableCookies", "areCookiesMutableInCurrentPhase", "getModifiedCookieValues", "responseCookiesToRequestCookies", "wrapWithMutableAccessCheck", "Error", "constructor", "callable", "seal", "cookies", "Proxy", "get", "target", "prop", "receiver", "ReflectAdapter", "SYMBOL_MODIFY_COOKIE_VALUES", "Symbol", "for", "modified", "Array", "isArray", "length", "headers", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resCookies", "ResponseCookies", "returnedCookies", "getAll", "cookie", "set", "wrap", "onUpdateCookies", "responseCookies", "Headers", "modifiedV<PERSON>ues", "modifiedCookies", "Set", "updateResponseCookies", "workStore", "workAsyncStorage", "getStore", "pathWasRevalidated", "allCookies", "filter", "c", "has", "name", "serializedCookies", "tempCookies", "push", "toString", "wrappedCookies", "args", "add", "delete", "ensureCookiesAreStillMutable", "requestStore", "phase", "callingExpression", "getExpectedRequestStore", "requestCookies", "RequestCookies"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IAyGaA,4BAA4B;eAA5BA;;IA5FAC,2BAA2B;eAA3BA;;IAwBAC,qBAAqB;eAArBA;;IAoCGC,oBAAoB;eAApBA;;IAwIAC,+BAA+B;eAA/BA;;IAzJAC,uBAAuB;eAAvBA;;IA4KAC,+BAA+B;eAA/BA;;IA9CAC,0BAA0B;eAA1BA;;;yBAtLe;yBAGA;0CACE;8CAI1B;AAKA,MAAMN,oCAAoCO;IAC/CC,aAAc;QACZ,KAAK,CACH;IAEJ;IAEA,OAAcC,WAAW;QACvB,MAAM,IAAIT;IACZ;AACF;AAcO,MAAMC;IACX,OAAcS,KAAKC,OAAuB,EAA0B;QAClE,OAAO,IAAIC,MAAMD,SAAgB;YAC/BE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,OAAOf,4BAA4BS,QAAQ;oBAC7C;wBACE,OAAOQ,uBAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;AACF;AAEA,MAAME,8BAA8BC,OAAOC,GAAG,CAAC;AAExC,SAAShB,wBACdO,OAAwB;IAExB,MAAMU,WAAyC,AAACV,OAA0B,CACxEO,4BACD;IACD,IAAI,CAACG,YAAY,CAACC,MAAMC,OAAO,CAACF,aAAaA,SAASG,MAAM,KAAK,GAAG;QAClE,OAAO,EAAE;IACX;IAEA,OAAOH;AACT;AAMO,SAASnB,qBACduB,OAAgB,EAChBC,cAA+B;IAE/B,MAAMC,uBAAuBvB,wBAAwBsB;IACrD,IAAIC,qBAAqBH,MAAM,KAAK,GAAG;QACrC,OAAO;IACT;IAEA,uDAAuD;IACvD,mDAAmD;IACnD,8BAA8B;IAC9B,MAAMI,aAAa,IAAIC,wBAAe,CAACJ;IACvC,MAAMK,kBAAkBF,WAAWG,MAAM;IAEzC,yCAAyC;IACzC,KAAK,MAAMC,UAAUL,qBAAsB;QACzCC,WAAWK,GAAG,CAACD;IACjB;IAEA,gDAAgD;IAChD,KAAK,MAAMA,UAAUF,gBAAiB;QACpCF,WAAWK,GAAG,CAACD;IACjB;IAEA,OAAO;AACT;AAMO,MAAMjC;IACX,OAAcmC,KACZvB,OAAuB,EACvBwB,eAA6C,EAC5B;QACjB,MAAMC,kBAAkB,IAAIP,wBAAe,CAAC,IAAIQ;QAChD,KAAK,MAAML,UAAUrB,QAAQoB,MAAM,GAAI;YACrCK,gBAAgBH,GAAG,CAACD;QACtB;QAEA,IAAIM,iBAAmC,EAAE;QACzC,MAAMC,kBAAkB,IAAIC;QAC5B,MAAMC,wBAAwB;YAC5B,+CAA+C;YAC/C,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;YAC3C,IAAIF,WAAW;gBACbA,UAAUG,kBAAkB,GAAG;YACjC;YAEA,MAAMC,aAAaV,gBAAgBL,MAAM;YACzCO,iBAAiBQ,WAAWC,MAAM,CAAC,CAACC,IAAMT,gBAAgBU,GAAG,CAACD,EAAEE,IAAI;YACpE,IAAIf,iBAAiB;gBACnB,MAAMgB,oBAA8B,EAAE;gBACtC,KAAK,MAAMnB,UAAUM,eAAgB;oBACnC,MAAMc,cAAc,IAAIvB,wBAAe,CAAC,IAAIQ;oBAC5Ce,YAAYnB,GAAG,CAACD;oBAChBmB,kBAAkBE,IAAI,CAACD,YAAYE,QAAQ;gBAC7C;gBAEAnB,gBAAgBgB;YAClB;QACF;QAEA,MAAMI,iBAAiB,IAAI3C,MAAMwB,iBAAiB;YAChDvB,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,qDAAqD;oBACrD,KAAKG;wBACH,OAAOoB;oBAET,iEAAiE;oBACjE,yBAAyB;oBACzB,KAAK;wBACH,OAAO,SAAU,GAAGkB,IAAiC;4BACnDjB,gBAAgBkB,GAAG,CACjB,OAAOD,IAAI,CAAC,EAAE,KAAK,WAAWA,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,CAACN,IAAI;4BAEtD,IAAI;gCACFpC,OAAO4C,MAAM,IAAIF;gCACjB,OAAOD;4BACT,SAAU;gCACRd;4BACF;wBACF;oBACF,KAAK;wBACH,OAAO,SAAU,GAAGe,IAAmB;4BACrCjB,gBAAgBkB,GAAG,CACjB,OAAOD,IAAI,CAAC,EAAE,KAAK,WAAWA,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,CAACN,IAAI;4BAEtD,IAAI;gCACFpC,OAAOmB,GAAG,IAAIuB;gCACd,OAAOD;4BACT,SAAU;gCACRd;4BACF;wBACF;oBAEF;wBACE,OAAOxB,uBAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;QAEA,OAAOuC;IACT;AACF;AAEO,SAASjD,2BACd8B,eAAgC;IAEhC,MAAMmB,iBAAiB,IAAI3C,MAAMwB,iBAAiB;QAChDvB,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,OAAQD;gBACN,KAAK;oBACH,OAAO,SAAU,GAAGyC,IAAiC;wBACnDG,6BAA6B;wBAC7B7C,OAAO4C,MAAM,IAAIF;wBACjB,OAAOD;oBACT;gBACF,KAAK;oBACH,OAAO,SAAU,GAAGC,IAAmB;wBACrCG,6BAA6B;wBAC7B7C,OAAOmB,GAAG,IAAIuB;wBACd,OAAOD;oBACT;gBAEF;oBACE,OAAOtC,uBAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;YAC5C;QACF;IACF;IACA,OAAOuC;AACT;AAEO,SAASpD,gCAAgCyD,YAA0B;IACxE,OAAOA,aAAaC,KAAK,KAAK;AAChC;AAEA;;;;;;GAMG,GACH,SAASF,6BAA6BG,iBAAyB;IAC7D,MAAMF,eAAeG,IAAAA,qDAAuB,EAACD;IAC7C,IAAI,CAAC3D,gCAAgCyD,eAAe;QAClD,mFAAmF;QACnF,MAAM,IAAI5D;IACZ;AACF;AAEO,SAASK,gCACd+B,eAAgC;IAEhC,MAAM4B,iBAAiB,IAAIC,uBAAc,CAAC,IAAI5B;IAC9C,KAAK,MAAML,UAAUI,gBAAgBL,MAAM,GAAI;QAC7CiC,eAAe/B,GAAG,CAACD;IACrB;IACA,OAAOgC;AACT"}