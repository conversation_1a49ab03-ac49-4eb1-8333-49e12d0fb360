{"version": 3, "sources": ["../../../src/server/lib/render-server.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, UpgradeHandler } from '../next'\nimport type { DevBundlerService } from './dev-bundler-service'\nimport type { PropagateToWorkersField } from './router-utils/types'\n\nimport next from '../next'\nimport type { Span } from '../../trace'\n\nexport type ServerInitResult = {\n  requestHandler: RequestHandler\n  upgradeHandler: UpgradeHandler\n  server: NextServer\n  // Make an effort to close upgraded HTTP requests (e.g. Turbopack HMR websockets)\n  closeUpgraded: () => void\n}\n\nlet initializations: Record<string, Promise<ServerInitResult> | undefined> = {}\n\nlet sandboxContext: undefined | typeof import('../web/sandbox/context')\n\nif (process.env.NODE_ENV !== 'production') {\n  sandboxContext = require('../web/sandbox/context')\n}\n\nexport function clearAllModuleContexts() {\n  return sandboxContext?.clearAllModuleContexts()\n}\n\nexport function clearModuleContext(target: string) {\n  return sandboxContext?.clearModuleContext(target)\n}\n\nexport async function getServerField(\n  dir: string,\n  field: PropagateToWorkersField\n) {\n  const initialization = await initializations[dir]\n  if (!initialization) {\n    throw new Error('Invariant cant propagate server field, no app initialized')\n  }\n  const { server } = initialization\n  let wrappedServer = server['server']! // NextServer.server is private\n  return wrappedServer[field as keyof typeof wrappedServer]\n}\n\nexport async function propagateServerField(\n  dir: string,\n  field: PropagateToWorkersField,\n  value: any\n) {\n  const initialization = await initializations[dir]\n  if (!initialization) {\n    throw new Error('Invariant cant propagate server field, no app initialized')\n  }\n  const { server } = initialization\n  let wrappedServer = server['server']\n  const _field = field as keyof NonNullable<typeof wrappedServer>\n\n  if (wrappedServer) {\n    if (typeof wrappedServer[_field] === 'function') {\n      // @ts-expect-error\n      await wrappedServer[_field].apply(\n        wrappedServer,\n        Array.isArray(value) ? value : []\n      )\n    } else {\n      // @ts-expect-error\n      wrappedServer[_field] = value\n    }\n  }\n}\n\nasync function initializeImpl(opts: {\n  dir: string\n  port: number\n  dev: boolean\n  minimalMode?: boolean\n  hostname?: string\n  keepAliveTimeout?: number\n  serverFields?: any\n  server?: any\n  experimentalTestProxy: boolean\n  experimentalHttpsServer: boolean\n  _ipcPort?: string\n  _ipcKey?: string\n  bundlerService: DevBundlerService | undefined\n  startServerSpan: Span | undefined\n  quiet?: boolean\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n}): Promise<ServerInitResult> {\n  const type = process.env.__NEXT_PRIVATE_RENDER_WORKER\n  if (type) {\n    process.title = 'next-render-worker-' + type\n  }\n\n  let requestHandler: RequestHandler\n  let upgradeHandler: UpgradeHandler\n\n  const server = next({\n    ...opts,\n    hostname: opts.hostname || 'localhost',\n    customServer: false,\n    httpServer: opts.server,\n    port: opts.port,\n  }) as NextServer // should return a NextServer when `customServer: false`\n  requestHandler = server.getRequestHandler()\n  upgradeHandler = server.getUpgradeHandler()\n\n  await server.prepare(opts.serverFields)\n\n  return {\n    requestHandler,\n    upgradeHandler,\n    server,\n    closeUpgraded() {\n      opts.bundlerService?.close()\n    },\n  }\n}\n\nexport async function initialize(\n  opts: Parameters<typeof initializeImpl>[0]\n): Promise<ServerInitResult> {\n  // if we already setup the server return as we only need to do\n  // this on first worker boot\n  if (initializations[opts.dir]) {\n    return initializations[opts.dir]!\n  }\n  return (initializations[opts.dir] = initializeImpl(opts))\n}\n"], "names": ["clearAllModuleContexts", "clearModuleContext", "getServerField", "initialize", "propagateServerField", "initializations", "sandboxContext", "process", "env", "NODE_ENV", "require", "target", "dir", "field", "initialization", "Error", "server", "wrappedServer", "value", "_field", "apply", "Array", "isArray", "initializeImpl", "opts", "type", "__NEXT_PRIVATE_RENDER_WORKER", "title", "requestHandler", "upgradeHandler", "next", "hostname", "customServer", "httpServer", "port", "getRequestHandler", "getUpgradeHandler", "prepare", "serverFields", "closeUpgraded", "bundlerService", "close"], "mappings": ";;;;;;;;;;;;;;;;;;IAuBgBA,sBAAsB;eAAtBA;;IAIAC,kBAAkB;eAAlBA;;IAIMC,cAAc;eAAdA;;IAwFAC,UAAU;eAAVA;;IA3EAC,oBAAoB;eAApBA;;;6DAxCL;;;;;;AAWjB,IAAIC,kBAAyE,CAAC;AAE9E,IAAIC;AAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCH,iBAAiBI,QAAQ;AAC3B;AAEO,SAASV;IACd,OAAOM,kCAAAA,eAAgBN,sBAAsB;AAC/C;AAEO,SAASC,mBAAmBU,MAAc;IAC/C,OAAOL,kCAAAA,eAAgBL,kBAAkB,CAACU;AAC5C;AAEO,eAAeT,eACpBU,GAAW,EACXC,KAA8B;IAE9B,MAAMC,iBAAiB,MAAMT,eAAe,CAACO,IAAI;IACjD,IAAI,CAACE,gBAAgB;QACnB,MAAM,qBAAsE,CAAtE,IAAIC,MAAM,8DAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAqE;IAC7E;IACA,MAAM,EAAEC,MAAM,EAAE,GAAGF;IACnB,IAAIG,gBAAgBD,MAAM,CAAC,SAAS,AAAE,+BAA+B;;IACrE,OAAOC,aAAa,CAACJ,MAAoC;AAC3D;AAEO,eAAeT,qBACpBQ,GAAW,EACXC,KAA8B,EAC9BK,KAAU;IAEV,MAAMJ,iBAAiB,MAAMT,eAAe,CAACO,IAAI;IACjD,IAAI,CAACE,gBAAgB;QACnB,MAAM,qBAAsE,CAAtE,IAAIC,MAAM,8DAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAqE;IAC7E;IACA,MAAM,EAAEC,MAAM,EAAE,GAAGF;IACnB,IAAIG,gBAAgBD,MAAM,CAAC,SAAS;IACpC,MAAMG,SAASN;IAEf,IAAII,eAAe;QACjB,IAAI,OAAOA,aAAa,CAACE,OAAO,KAAK,YAAY;YAC/C,mBAAmB;YACnB,MAAMF,aAAa,CAACE,OAAO,CAACC,KAAK,CAC/BH,eACAI,MAAMC,OAAO,CAACJ,SAASA,QAAQ,EAAE;QAErC,OAAO;YACL,mBAAmB;YACnBD,aAAa,CAACE,OAAO,GAAGD;QAC1B;IACF;AACF;AAEA,eAAeK,eAAeC,IAiB7B;IACC,MAAMC,OAAOlB,QAAQC,GAAG,CAACkB,4BAA4B;IACrD,IAAID,MAAM;QACRlB,QAAQoB,KAAK,GAAG,wBAAwBF;IAC1C;IAEA,IAAIG;IACJ,IAAIC;IAEJ,MAAMb,SAASc,IAAAA,aAAI,EAAC;QAClB,GAAGN,IAAI;QACPO,UAAUP,KAAKO,QAAQ,IAAI;QAC3BC,cAAc;QACdC,YAAYT,KAAKR,MAAM;QACvBkB,MAAMV,KAAKU,IAAI;IACjB,EAAiB,wDAAwD;;IACzEN,iBAAiBZ,OAAOmB,iBAAiB;IACzCN,iBAAiBb,OAAOoB,iBAAiB;IAEzC,MAAMpB,OAAOqB,OAAO,CAACb,KAAKc,YAAY;IAEtC,OAAO;QACLV;QACAC;QACAb;QACAuB;gBACEf;aAAAA,uBAAAA,KAAKgB,cAAc,qBAAnBhB,qBAAqBiB,KAAK;QAC5B;IACF;AACF;AAEO,eAAetC,WACpBqB,IAA0C;IAE1C,8DAA8D;IAC9D,4BAA4B;IAC5B,IAAInB,eAAe,CAACmB,KAAKZ,GAAG,CAAC,EAAE;QAC7B,OAAOP,eAAe,CAACmB,KAAKZ,GAAG,CAAC;IAClC;IACA,OAAQP,eAAe,CAACmB,KAAKZ,GAAG,CAAC,GAAGW,eAAeC;AACrD"}