{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_5399b416._.js", "server/edge/chunks/node_modules_@auth_core_5ebafa38._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_e184ff1b._.js", "server/edge/chunks/[root-of-the-server]__df53d061._.js", "server/edge/chunks/edge-wrapper_3d09a47d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "TvVIxASkkslauaDKSupofnfB/7vn8nVj3kHQANArkl0=", "__NEXT_PREVIEW_MODE_ID": "1997e8f998e67f00721b28259270126c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ffda9779d54bc06e1548434bce94d139874ad7dd35a5f5e4b14e7e07a66b2001", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4170743c8d0955fe80ae83865ff60701d2078b07e1d85471cb5bfe9d11b32a33"}}}, "instrumentation": null, "functions": {}}