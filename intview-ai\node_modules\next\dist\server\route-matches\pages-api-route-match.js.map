{"version": 3, "sources": ["../../../src/server/route-matches/pages-api-route-match.ts"], "sourcesContent": ["import type { RouteMatch } from './route-match'\nimport type { PagesAPIRouteDefinition } from '../route-definitions/pages-api-route-definition'\n\nimport { RouteKind } from '../route-kind'\n\nexport interface PagesAPIRouteMatch\n  extends RouteMatch<PagesAPIRouteDefinition> {}\n\n/**\n * Checks if the given match is a Pages API route match.\n * @param match the match to check\n * @returns true if the match is a Pages API route match, false otherwise\n */\nexport function isPagesAPIRouteMatch(\n  match: RouteMatch\n): match is PagesAPIRouteMatch {\n  return match.definition.kind === RouteKind.PAGES_API\n}\n"], "names": ["isPagesAPIRouteMatch", "match", "definition", "kind", "RouteKind", "PAGES_API"], "mappings": ";;;;+BAagBA;;;eAAAA;;;2BAVU;AAUnB,SAASA,qBACdC,KAAiB;IAEjB,OAAOA,MAAMC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACC,SAAS;AACtD"}