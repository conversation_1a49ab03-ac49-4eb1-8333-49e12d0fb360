{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/forms/SocialAuthForm.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\n// import { Button } from \"../ui/button\";\r\nimport Image from \"next/image\";\r\n// import { toast } from \"sonner\";\r\n// import { signIn } from \"next-auth/react\";\r\n// import ROUTES from \"@/constants/routes\";\r\n\r\nconst SocialAuthForm = () => {\r\n  // const buttonClassName = `background-dark400_light900 body-medium text-dark200_light800\r\n  // rounded-2  min-h-12 flex-1 px-4 py-3`;\r\n\r\n  //   const handleSignIn = (provider: \"github\" | \"google\") => {\r\n  //     console.log(\"Handle github\");\r\n\r\n  //   try {\r\n  //     // signIn(provider, {\r\n  //     //   redirectTo: ROUTES.HOME,\r\n  //     // redirect: false,\r\n  //     // });\r\n  //   } catch (error) {\r\n  //     console.log(error);\r\n  //     toast.error(\r\n  //       error instanceof Error\r\n  //         ? error.message\r\n  //         : \"An error occoured during sign-in\"\r\n  //     );\r\n  //   }\r\n  // };\r\n\r\n  return (\r\n    <div className=\"flex flex-wrap mt-10 gap-2.5\">\r\n      <p className=\"mr-10\">Or Continue with </p>\r\n      {/* <Button\r\n        // className={buttonClassName}\r\n        onClick={() => handleSignIn(\"github\")}\r\n      > */}\r\n      <Image\r\n        src={\"/icons/facebook.svg\"}\r\n        alt=\"Facebook\"\r\n        width={20}\r\n        height={20}\r\n        className=\"object-contain mr-2.5 \"\r\n      />\r\n      {/* </Button> */}\r\n      {/* <Button\r\n        // className={buttonClassName}\r\n        onClick={() => handleSignIn(\"google\")}\r\n      > */}\r\n      <Image\r\n        src={\"/icons/google.svg\"}\r\n        alt=\"Google\"\r\n        width={20}\r\n        height={20}\r\n        className=\"object-contain mr-2.5\"\r\n      />\r\n      {/* </Button> */}\r\n\r\n      <Image\r\n        src={\"/icons/apple.svg\"}\r\n        alt=\"Apple\"\r\n        width={20}\r\n        height={20}\r\n        className=\"object-contain mr-2.5 \"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SocialAuthForm;\r\n"], "names": [], "mappings": ";;;;AAEA,yCAAyC;AACzC;AAHA;;;AAIA,kCAAkC;AAClC,4CAA4C;AAC5C,2CAA2C;AAE3C,MAAM,iBAAiB;IACrB,yFAAyF;IACzF,yCAAyC;IAEzC,8DAA8D;IAC9D,oCAAoC;IAEpC,UAAU;IACV,4BAA4B;IAC5B,oCAAoC;IACpC,0BAA0B;IAC1B,aAAa;IACb,sBAAsB;IACtB,0BAA0B;IAC1B,mBAAmB;IACnB,+BAA+B;IAC/B,0BAA0B;IAC1B,+CAA+C;IAC/C,SAAS;IACT,MAAM;IACN,KAAK;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAAQ;;;;;;0BAKrB,6LAAC,gIAAA,CAAA,UAAK;gBACJ,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;gBACR,WAAU;;;;;;0BAOZ,6LAAC,gIAAA,CAAA,UAAK;gBACJ,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;gBACR,WAAU;;;;;;0BAIZ,6LAAC,gIAAA,CAAA,UAAK;gBACJ,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;gBACR,WAAU;;;;;;;;;;;;AAIlB;KA3DM;uCA6DS", "debugId": null}}]}