{"version": 3, "sources": ["../../../src/server/node-environment-extensions/random.tsx"], "sourcesContent": ["/**\n * We extend Math.random() during builds and revalidates to ensure that prerenders don't observe randomness\n * When dynamicIO is enabled. randomness is a form of IO even though it resolves synchronously. When dyanmicIO is\n * enabled we need to ensure that randomness is excluded from prerenders.\n *\n * The extensions here never error nor alter the random generation itself and thus should be transparent to callers.\n */\n\nimport { io } from './utils'\n\nconst expression = '`Math.random()`'\ntry {\n  const _random = Math.random\n  Math.random = function random() {\n    io(expression, 'random')\n    return _random.apply(null, arguments as any)\n\n    // We bind here to alter the `toString` printing to match `Math.random`'s native `toString`.\n    // eslint-disable-next-line no-extra-bind\n  }.bind(null)\n  Object.defineProperty(Math.random, 'name', { value: 'random' })\n} catch {\n  console.error(\n    `Failed to install ${expression} extension. When using \\`experimental.dynamicIO\\` calling this function will not correctly trigger dynamic behavior.`\n  )\n}\n"], "names": ["expression", "_random", "Math", "random", "io", "apply", "arguments", "bind", "Object", "defineProperty", "value", "console", "error"], "mappings": "AAAA;;;;;;CAMC;;;;uBAEkB;AAEnB,MAAMA,aAAa;AACnB,IAAI;IACF,MAAMC,UAAUC,KAAKC,MAAM;IAC3BD,KAAKC,MAAM,GAAG,CAAA,SAASA;QACrBC,IAAAA,SAAE,EAACJ,YAAY;QACf,OAAOC,QAAQI,KAAK,CAAC,MAAMC;IAE3B,4FAA4F;IAC5F,yCAAyC;IAC3C,CAAA,EAAEC,IAAI,CAAC;IACPC,OAAOC,cAAc,CAACP,KAAKC,MAAM,EAAE,QAAQ;QAAEO,OAAO;IAAS;AAC/D,EAAE,OAAM;IACNC,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEZ,WAAW,oHAAoH,CAAC;AAEzJ"}